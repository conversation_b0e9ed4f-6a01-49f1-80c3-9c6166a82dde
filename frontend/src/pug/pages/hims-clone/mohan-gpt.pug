extends ../../layouts/default.pug

block config
    - var metaDescription = 'Browse medications & treatments | TeleTest'
    - var pageTitle = 'Medications & Treatments | TeleTest'

append css
    style.
        .catalog-bg { background: #f6f7fb; }
        .catalog-title { font-weight: 800; letter-spacing: -0.02em; }
        .catalog-title .accent { color: #ff8a65; }
        .search-rounded { border-radius: 999px; height: 48px; }
        .product-card { background: #fff; border: 1px solid #e7e7ea; border-radius: 16px; transition: box-shadow .2s ease, transform .2s ease; }
        .product-card:hover { box-shadow: 0 8px 20px rgba(0,0,0,.06); transform: translateY(-2px); }
        .product-img { width: 80px; height: 80px; border-radius: 12px; background: #f2f3f7; display: flex; align-items: center; justify-content: center; overflow: hidden; }
        .product-img img { max-width: 64px; max-height: 64px; object-fit: contain; }
        .price { font-size: 1.25rem; font-weight: 700; color: #111827; }
        .pill-btn { width: 40px; height: 40px; display: inline-flex; align-items: center; justify-content: center; border-radius: 999px; }

block content
    //- * * * * * * * *
    //- * * Navbar   * *
    //- * * * * * * * *
    include ../mixins/navbar.pug
    +navbar({
        navbarBg: 'bg-white',
        navbarStyle: 'navbar-light',
        navbarBrandColor: 'text-dark',
        navbarBtnColor: 'btn-red',
        navbarContainer: 'container',
    })

    section.catalog-bg.py-5
        .container
            .text-center.mb-4
                h1.catalog-title.mb-3
                    span.accent Check our
                    |  Medications & Treatments
                .mx-auto(style='max-width: 560px;')
                    input#med-search.form-control.form-control-lg.search-rounded(type='text', placeholder='Search Products')

            //- Catalog grid

            .row.g-3.g-md-4
                //- Tadalafil Capsule
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Capsule 3mg', data-type='Tadalafil', data-price='2.25')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-blue.png', alt='Tadalafil Capsule 3mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Capsule 3mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • Up to 36h duration
                                .price $2.25
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Capsule 5mg', data-type='Tadalafil', data-price='3.00')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-blue.png', alt='Tadalafil Capsule 5mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Capsule 5mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • Up to 36h duration
                                .price $3.00
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Capsule 6mg', data-type='Tadalafil', data-price='3.25')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-blue.png', alt='Tadalafil Capsule 6mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Capsule 6mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • Up to 36h duration
                                .price $3.25
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Capsule 10mg', data-type='Tadalafil', data-price='4.50')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-blue.png', alt='Tadalafil Capsule 10mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Capsule 10mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • Up to 36h duration
                                .price $4.50
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Capsule 20mg', data-type='Tadalafil', data-price='6.50')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-blue.png', alt='Tadalafil Capsule 20mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Capsule 20mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • Up to 36h duration
                                .price $6.50
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white

                //- Tadalafil Tablet
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Tablet 5mg', data-type='Tadalafil', data-price='4.01')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round.png', alt='Tadalafil Tablet 5mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Tablet 5mg
                                p.text-muted.small.mb-2 Generic • 30-45 min onset • Up to 36h duration
                                .price $4.01
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Tablet 10mg', data-type='Tadalafil', data-price='13.12')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round.png', alt='Tadalafil Tablet 10mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Tablet 10mg
                                p.text-muted.small.mb-2 Generic • 30-45 min onset • Up to 36h duration
                                .price $13.12
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Tadalafil Tablet 20mg', data-type='Tadalafil', data-price='13.12')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round.png', alt='Tadalafil Tablet 20mg')
                                h3.h6.fw-semibold.mb-1 Tadalafil Tablet 20mg
                                p.text-muted.small.mb-2 Generic • 30-45 min onset • Up to 36h duration
                                .price $13.12
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white

                //- Sildenafil Capsule
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Capsule 25mg', data-type='Sildenafil', data-price='3.25')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-white.png', alt='Sildenafil Capsule 25mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Capsule 25mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • 4-6h duration
                                .price $3.25
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Capsule 50mg', data-type='Sildenafil', data-price='3.75')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-white.png', alt='Sildenafil Capsule 50mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Capsule 50mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • 4-6h duration
                                .price $3.75
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Capsule 100mg', data-type='Sildenafil', data-price='4.50')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-white.png', alt='Sildenafil Capsule 100mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Capsule 100mg
                                p.text-muted.small.mb-2 Compounded • Lactose-free • 4-6h duration
                                .price $4.50
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white

                //- Sildenafil Tablet
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Tablet 25mg', data-type='Sildenafil', data-price='9.12')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round-blue.png', alt='Sildenafil Tablet 25mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Tablet 25mg
                                p.text-muted.small.mb-2 Generic • 30-60 min onset • 4-6h duration
                                .price $9.12
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Tablet 50mg', data-type='Sildenafil', data-price='9.73')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round-blue.png', alt='Sildenafil Tablet 50mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Tablet 50mg
                                p.text-muted.small.mb-2 Generic • 30-60 min onset • 4-6h duration
                                .price $9.73
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white
                .col-12.col-md-6.col-lg-4
                    .product-card.p-3.h-100.med-card(data-name='Sildenafil Tablet 100mg', data-type='Sildenafil', data-price='10.12')
                        .d-flex.align-items-start.justify-content-between.gap-3
                            .d-flex.flex-column.flex-grow-1
                                .product-img.mb-3
                                    img(src='/assets/img/pills/2025-05-08-pill-round-blue.png', alt='Sildenafil Tablet 100mg')
                                h3.h6.fw-semibold.mb-1 Sildenafil Tablet 100mg
                                p.text-muted.small.mb-2 Generic • 30-60 min onset • 4-6h duration
                                .price $10.12
                            button.btn.btn-dark.pill-btn.ms-3(type='button', aria-label='Add')
                                i.fas.fa-plus.text-white

            //- Product Cart Modal
            .modal.fade#productCartModal(tabindex='-1' aria-labelledby='productCartModalLabel' aria-hidden='true')
                .modal-dialog.modal-dialog-centered
                    .modal-content
                        .modal-header
                            .d-flex.flex-column
                                small.text-muted.mb-1 CART
                                h5.modal-title#modalProductName Product
                            button.btn-close(type='button', data-bs-dismiss='modal', aria-label='Close')
                        .modal-body
                            .d-flex.justify-content-between.align-items-start.mb-3
                                //- Quantity controls
                                .btn-group(aria-label='Quantity')
                                    button#qtyMinus.btn.btn-outline-secondary(type='button') -
                                    span#qtyValue.btn.btn-outline-secondary.disabled 1
                                    button#qtyPlus.btn.btn-outline-secondary(type='button') +
                                .text-end
                                    .h6.mb-1#modalProductPrice $0.00
                                    a.small.text-muted(href='#' id='removeItem') Remove
                            //- Supply options
                            .mb-2
                                .d-flex.flex-wrap.gap-2
                                    input#supply1.btn-check(type='radio', name='supply', value='1m', checked)
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supply1') 1 month supply
                                    input#supply3.btn-check(type='radio', name='supply', value='3m')
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supply3') 3 month supply
                                    input#supply6.btn-check(type='radio', name='supply', value='6m')
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supply6') 6 month supply
                                    input#supply270.btn-check(type='radio', name='supply', value='270d')
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supply270') 270 day supply
                                    input#supplyq3.btn-check(type='radio', name='supply', value='q3m')
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supplyq3') Every 3 months
                                    input#supplyq6.btn-check(type='radio', name='supply', value='q6m')
                                    label.btn.btn-outline-secondary.btn-sm.rounded-pill(for='supplyq6') Every 6 months
                            p.small.text-muted.mb-0 Choose the plan that suites your needs
                        .modal-footer.flex-column.align-items-stretch
                            .d-flex.justify-content-between.align-items-center.w-100.bg-light.rounded.p-3.mb-2
                                span.small Subtotal
                                strong#modalSubtotal $0.00
                            button.btn.btn-dark.btn-lg.w-100(type='button' id='checkoutBtn') Checkout

            //- Footnote
            .mt-5.text-center
                p.small.text-muted.mb-0
                    | TeleTest works with a compounding pharmacy that offers lactose-free options. Common side effects may include headache, flushing, and nasal congestion. Tadalafil products may also cause back-pain or muscle-aches.

block footer
    include ../mixins/footer.pug
    +footer({ footerBg: 'bg-white', footerStyle: 'footer-light' })

append scripts
    //- Filtering + modal behavior
    script.
      document.addEventListener('DOMContentLoaded', function(){
        var input = document.getElementById('med-search');
        var cards = Array.prototype.slice.call(document.querySelectorAll('.med-card'));
        if(input){
          input.addEventListener('input', function(){
            var q = input.value.toLowerCase();
            cards.forEach(function(card){
              var name = (card.getAttribute('data-name')||'').toLowerCase();
              var type = (card.getAttribute('data-type')||'').toLowerCase();
              var show = !q || name.indexOf(q) > -1 || type.indexOf(q) > -1;
              card.parentElement.classList.toggle('d-none', !show);
            });
          });
        }

        // Modal
        var modalEl = document.getElementById('productCartModal');
        var modal = modalEl && window.bootstrap ? new bootstrap.Modal(modalEl) : null;
        var qty = 1; var price = 0;
        function fmt(v){ return '$' + v.toFixed(2); }
        function update(){
          document.getElementById('qtyValue').innerText = qty;
          document.getElementById('modalSubtotal').innerText = fmt(price * qty);
        }
        document.querySelectorAll('.med-card .pill-btn').forEach(function(btn){
          btn.addEventListener('click', function(){
            var card = btn.closest('.med-card');
            var name = card.getAttribute('data-name');
            price = parseFloat(card.getAttribute('data-price')) || 0;
            qty = 1;
            document.getElementById('modalProductName').innerText = name;
            document.getElementById('modalProductPrice').innerText = fmt(price);
            update();
            if(modal) modal.show();
          });
        });
        var minus = document.getElementById('qtyMinus');
        var plus = document.getElementById('qtyPlus');
        if(minus) minus.addEventListener('click', function(){ if(qty>1){ qty--; update(); }});
        if(plus) plus.addEventListener('click', function(){ qty++; update(); });
      });

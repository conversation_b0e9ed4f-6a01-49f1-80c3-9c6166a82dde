// import  axios  from 'https://unpkg.com/axios@0.21.1/dist/axios.js';
// https://www.digitalocean.com/community/tutorials/vuejs-rest-api-axios
export default {
  name: `App`,
  data() {
    return {
      updateNeeded: false,
      modified: 0,
      htmls: [],
      htmlsCache: [],
    }
  },
  // Fetches posts when the component is created.
  async created() {
    console.log('axios component created');
    this.updateCount = 0;
    await this.pollData();
    // await this.updateData();
    this.timer = setInterval(this.pollData, 2000);
  },
  methods: {
    async pollData() {
      if (++this.updateCount >= 25) {
        this.cancelAutoUpdate();
      }

      console.log('pollData');
      console.log(`updateCount: ${this.updateCount}`);
      try {
        const response = await axios.get(`/app/q-htmls/`);
        this.htmlsCache = response.data.htmls;
        if (this.modified != response.data.modified) {
          this.updateNeeded = true;
          this.modified = response.data.modified;
        }

        // if just paid = true but there's unpaid, wait longer for payment to go through
        const urlSearchParams = new URLSearchParams(window.location.search);

        const justPaid = window.location.search.indexOf('justPaid=true') > -1;
        if (this.updateNeeded && (!justPaid || this.updateCount > 3)) {
          this.updateData();
        }
      } catch (e) {
        // this.errors.push(e);
        console.log(e);
      }
    },
    async updateData() {
      console.log(`updateData`);

      const spinner = document.getElementById("loading-spinner");
      spinner.style.display = "none";
      this.updateNeeded = false;
      this.htmls = this.htmlsCache;
    },
    cancelAutoUpdate () {
      clearInterval(this.timer);
    }
  },
  beforeUnmount () {
    this.cancelAutoUpdate();
    this.updateData();
  },
  // watch: {
  //   modified: function(val) {
  //     console.log(`Modified changed: ${this.modified}`);
  //     this.updateData();
  //   }
  // },

  template: `
<div v-for="(html, index) of htmls">
  <div v-html="html.modal"></div>
  <hr v-if="index !== 0">
  <div v-html="html.row"></div>
</div>
`
};
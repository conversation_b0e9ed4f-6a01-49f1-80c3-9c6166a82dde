{"version": 3, "sources": ["webpack://feather/webpack/universalModuleDefinition", "webpack://feather/webpack/bootstrap", "webpack://feather/./node_modules/classnames/dedupe.js", "webpack://feather/./node_modules/core-js/es/array/from.js", "webpack://feather/./node_modules/core-js/internals/a-function.js", "webpack://feather/./node_modules/core-js/internals/an-object.js", "webpack://feather/./node_modules/core-js/internals/array-from.js", "webpack://feather/./node_modules/core-js/internals/array-includes.js", "webpack://feather/./node_modules/core-js/internals/bind-context.js", "webpack://feather/./node_modules/core-js/internals/call-with-safe-iteration-closing.js", "webpack://feather/./node_modules/core-js/internals/check-correctness-of-iteration.js", "webpack://feather/./node_modules/core-js/internals/classof-raw.js", "webpack://feather/./node_modules/core-js/internals/classof.js", "webpack://feather/./node_modules/core-js/internals/copy-constructor-properties.js", "webpack://feather/./node_modules/core-js/internals/correct-prototype-getter.js", "webpack://feather/./node_modules/core-js/internals/create-iterator-constructor.js", "webpack://feather/./node_modules/core-js/internals/create-property-descriptor.js", "webpack://feather/./node_modules/core-js/internals/create-property.js", "webpack://feather/./node_modules/core-js/internals/define-iterator.js", "webpack://feather/./node_modules/core-js/internals/descriptors.js", "webpack://feather/./node_modules/core-js/internals/document-create-element.js", "webpack://feather/./node_modules/core-js/internals/enum-bug-keys.js", "webpack://feather/./node_modules/core-js/internals/export.js", "webpack://feather/./node_modules/core-js/internals/fails.js", "webpack://feather/./node_modules/core-js/internals/function-to-string.js", "webpack://feather/./node_modules/core-js/internals/get-iterator-method.js", "webpack://feather/./node_modules/core-js/internals/global.js", "webpack://feather/./node_modules/core-js/internals/has.js", "webpack://feather/./node_modules/core-js/internals/hidden-keys.js", "webpack://feather/./node_modules/core-js/internals/hide.js", "webpack://feather/./node_modules/core-js/internals/html.js", "webpack://feather/./node_modules/core-js/internals/ie8-dom-define.js", "webpack://feather/./node_modules/core-js/internals/indexed-object.js", "webpack://feather/./node_modules/core-js/internals/internal-state.js", "webpack://feather/./node_modules/core-js/internals/is-array-iterator-method.js", "webpack://feather/./node_modules/core-js/internals/is-forced.js", "webpack://feather/./node_modules/core-js/internals/is-object.js", "webpack://feather/./node_modules/core-js/internals/is-pure.js", "webpack://feather/./node_modules/core-js/internals/iterators-core.js", "webpack://feather/./node_modules/core-js/internals/iterators.js", "webpack://feather/./node_modules/core-js/internals/native-symbol.js", "webpack://feather/./node_modules/core-js/internals/native-weak-map.js", "webpack://feather/./node_modules/core-js/internals/object-create.js", "webpack://feather/./node_modules/core-js/internals/object-define-properties.js", "webpack://feather/./node_modules/core-js/internals/object-define-property.js", "webpack://feather/./node_modules/core-js/internals/object-get-own-property-descriptor.js", "webpack://feather/./node_modules/core-js/internals/object-get-own-property-names.js", "webpack://feather/./node_modules/core-js/internals/object-get-own-property-symbols.js", "webpack://feather/./node_modules/core-js/internals/object-get-prototype-of.js", "webpack://feather/./node_modules/core-js/internals/object-keys-internal.js", "webpack://feather/./node_modules/core-js/internals/object-keys.js", "webpack://feather/./node_modules/core-js/internals/object-property-is-enumerable.js", "webpack://feather/./node_modules/core-js/internals/object-set-prototype-of.js", "webpack://feather/./node_modules/core-js/internals/own-keys.js", "webpack://feather/./node_modules/core-js/internals/path.js", "webpack://feather/./node_modules/core-js/internals/redefine.js", "webpack://feather/./node_modules/core-js/internals/require-object-coercible.js", "webpack://feather/./node_modules/core-js/internals/set-global.js", "webpack://feather/./node_modules/core-js/internals/set-to-string-tag.js", "webpack://feather/./node_modules/core-js/internals/shared-key.js", "webpack://feather/./node_modules/core-js/internals/shared.js", "webpack://feather/./node_modules/core-js/internals/string-at.js", "webpack://feather/./node_modules/core-js/internals/to-absolute-index.js", "webpack://feather/./node_modules/core-js/internals/to-indexed-object.js", "webpack://feather/./node_modules/core-js/internals/to-integer.js", "webpack://feather/./node_modules/core-js/internals/to-length.js", "webpack://feather/./node_modules/core-js/internals/to-object.js", "webpack://feather/./node_modules/core-js/internals/to-primitive.js", "webpack://feather/./node_modules/core-js/internals/uid.js", "webpack://feather/./node_modules/core-js/internals/validate-set-prototype-of-arguments.js", "webpack://feather/./node_modules/core-js/internals/well-known-symbol.js", "webpack://feather/./node_modules/core-js/modules/es.array.from.js", "webpack://feather/./node_modules/core-js/modules/es.string.iterator.js", "webpack://feather/(webpack)/buildin/global.js", "webpack://feather/./src/icon.js", "webpack://feather/./src/icons.js", "webpack://feather/./src/index.js", "webpack://feather/./src/replace.js", "webpack://feather/./src/to-svg.js"], "names": ["Icon", "name", "contents", "tags", "attrs", "DEFAULT_ATTRS", "class", "combinedAttrs", "attrsToString", "Object", "keys", "map", "key", "join", "icons", "reduce", "object", "icon", "module", "exports", "toSvg", "replace", "document", "Error", "elementsToReplace", "querySelectorAll", "Array", "from", "for<PERSON>ach", "replaceElement", "element", "elementAttrs", "getAttrs", "svgString", "svgDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "svgElement", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "attributes", "attr", "value", "console", "warn"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAK;AACL;AACA;;AAEA;AACA;AACA,yDAAiD,cAAc;AAC/D;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACnEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,kBAAkB,YAAY;AAC9B;AACA;AACA;;AAEA,iBAAiB;;AAEjB;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,kBAAkB,YAAY;AAC9B;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;;AAEA,OAAO;AACP,IAAI;AACJ;;AAEA;AACA,IAAI;AACJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,kBAAkB,SAAS;AAC3B;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA,EAAE;AACF;AACA;AACA;AACA,GAAG;AAAA;AACH,EAAE,QAEF;AACA,CAAC;;;;;;;;;;;;AC5GD;AACA;AACA;;AAEA;;;;;;;;;;;;ACJA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACJA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,+BAA+B;AACzC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,UAAU,eAAe;AACzB;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACxCA;AACA;AACA;;AAEA,qBAAqB,oBAAoB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,YAAY,eAAe;AAChC;AACA,KAAK;AACL;AACA;;;;;;;;;;;;AC1BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACZA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,SAAS,EAAE;AACzD,CAAC,gBAAgB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;ACrCA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;;;;;;ACJA;AACA;;AAEA;AACA;AACA,gDAAgD,kBAAkB,EAAE;;AAEpE;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACxBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA;;;;;;;;;;;;ACbA;;AAEA;AACA,gBAAgB;AAChB;AACA;AACA,CAAC;;;;;;;;;;;;;ACND;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;AACA,6DAA6D,0CAA0C;AACvG;AACA;AACA;AACA;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACPA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA,yCAAyC,4CAA4C;AACrF,6CAA6C,4CAA4C;AACzF,+CAA+C,4CAA4C;AAC3F,KAAK,qBAAqB,sCAAsC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB,mBAAmB;AACnC;AACA;AACA,yCAAyC,kCAAkC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,SAAS,qFAAqF;AACnG;;AAEA;AACA;;;;;;;;;;;;ACzFA;;AAEA;AACA;AACA,iCAAiC,QAAQ,mBAAmB,UAAU,EAAE,EAAE;AAC1E,CAAC;;;;;;;;;;;;ACLD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,mDAAmD;AACnD,GAAG;AACH,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrDA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;ACNA;;AAEA;;;;;;;;;;;;ACFA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACVA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACbA,uBAAuB;;AAEvB;AACA;AACA;;;;;;;;;;;;ACJA;;;;;;;;;;;;ACAA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;;;;;;;;;;;;ACTA;;AAEA;;AAEA;;;;;;;;;;;;ACJA;AACA;AACA;;AAEA;AACA;AACA;AACA,sBAAsB,UAAU;AAChC,GAAG;AACH,CAAC;;;;;;;;;;;;ACTD;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;;;;;;;;;;;ACZD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,uCAAuC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC5DA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;ACTA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;;ACpBA;AACA;AACA;;;;;;;;;;;;ACFA;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;AClCA;;;;;;;;;;;;ACAA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACND;AACA;;AAEA;;AAEA;;;;;;;;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,yBAAyB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;;;;;;;;;;;AC/CA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACbA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;ACjBA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;;;;;;;;;;ACRA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACfA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClBA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA,mCAAmC;AACnC;;AAEA;AACA,gFAAgF,OAAO;;AAEvF;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACVD;;AAEA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACnBD;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACZA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;;;;;;;;;;;ACtCD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACLA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;;;;;;;;;;;ACTA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,uCAAuC,iCAAiC;AACxE;AACA;;;;;;;;;;;;ACVA;AACA;;AAEA;;AAEA;AACA;AACA;;;;;;;;;;;;ACPA;AACA;AACA;;AAEA;AACA,kDAAkD;;AAElD;AACA,qEAAqE;AACrE,CAAC;AACD;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACbD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AChBA;;AAEA;AACA;;AAEA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;ACNA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACPA;;AAEA;;AAEA;AACA;AACA;AACA,uEAAuE;AACvE;;;;;;;;;;;;ACRA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACZA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;ACLA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA,GAAG,2DAA2D;AAC9D;AACA,CAAC;;;;;;;;;;;;;ACZD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA,UAAU;AACV,CAAC;;;;;;;;;;;;AC5BD;;AAEA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA,4CAA4C;;AAE5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBA;;;;AAEA;;;;;;;;IAEMA,I;AACJ,gBAAYC,IAAZ,EAAkBC,QAAlB,EAAuC;AAAA,QAAXC,IAAW,uEAAJ,EAAI;;AAAA;;AACrC,SAAKF,IAAL,GAAYA,IAAZ;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,KAAL,gBACKC,sBADL,EAEK,EAAEC,4BAA0BL,IAA5B,EAFL;AAID;;AAED;;;;;;;;;4BAKkB;AAAA,UAAZG,KAAY,uEAAJ,EAAI;;AAChB,UAAMG,6BACD,KAAKH,KADJ,EAEDA,KAFC,EAGD,EAAEE,OAAO,sBAAW,KAAKF,KAAL,CAAWE,KAAtB,EAA6BF,MAAME,KAAnC,CAAT,EAHC,CAAN;;AAMA,uBAAeE,cAAcD,aAAd,CAAf,SAA+C,KAAKL,QAApD;AACD;;AAED;;;;;;;;;;;+BAQW;AACT,aAAO,KAAKA,QAAZ;AACD;;;;;;AAGH;;;;;;;AAKA,SAASM,aAAT,CAAuBJ,KAAvB,EAA8B;AAC5B,SAAOK,OAAOC,IAAP,CAAYN,KAAZ,EACJO,GADI,CACA;AAAA,WAAUC,GAAV,UAAkBR,MAAMQ,GAAN,CAAlB;AAAA,GADA,EAEJC,IAFI,CAEC,GAFD,CAAP;AAGD;;kBAEcb,I;;;;;;;;;;;;;;;;;;ACtDf;;;;AACA;;;;AACA;;;;;;kBAEeS,OAAOC,IAAP,CAAYI,eAAZ,EACZH,GADY,CACR;AAAA,SAAO,IAAIX,cAAJ,CAASY,GAAT,EAAcE,gBAAMF,GAAN,CAAd,EAA0BT,eAAKS,GAAL,CAA1B,CAAP;AAAA,CADQ,EAEZG,MAFY,CAEL,UAACC,MAAD,EAASC,IAAT,EAAkB;AACxBD,SAAOC,KAAKhB,IAAZ,IAAoBgB,IAApB;AACA,SAAOD,MAAP;AACD,CALY,EAKV,EALU,C;;;;;;;;;;;;;;ACJf;;;;AACA;;;;AACA;;;;;;AAEAE,OAAOC,OAAP,GAAiB,EAAEL,sBAAF,EAASM,sBAAT,EAAgBC,0BAAhB,EAAjB,C;;;;;;;;;;;;;;;;;;kQCJA;;;AACA;;;;AAEA;;;;;;AAEA;;;;;AAKA,SAASA,OAAT,GAA6B;AAAA,MAAZjB,KAAY,uEAAJ,EAAI;;AAC3B,MAAI,OAAOkB,QAAP,KAAoB,WAAxB,EAAqC;AACnC,UAAM,IAAIC,KAAJ,CAAU,0DAAV,CAAN;AACD;;AAED,MAAMC,oBAAoBF,SAASG,gBAAT,CAA0B,gBAA1B,CAA1B;;AAEAC,QAAMC,IAAN,CAAWH,iBAAX,EAA8BI,OAA9B,CAAsC;AAAA,WACpCC,eAAeC,OAAf,EAAwB1B,KAAxB,CADoC;AAAA,GAAtC;AAGD;;AAED;;;;;;AAMA,SAASyB,cAAT,CAAwBC,OAAxB,EAA6C;AAAA,MAAZ1B,KAAY,uEAAJ,EAAI;;AAC3C,MAAM2B,eAAeC,SAASF,OAAT,CAArB;AACA,MAAM7B,OAAO8B,aAAa,cAAb,CAAb;AACA,SAAOA,aAAa,cAAb,CAAP;;AAEA,MAAME,YAAYnB,gBAAMb,IAAN,EAAYmB,KAAZ,cACbhB,KADa,EAEb2B,YAFa,EAGb,EAAEzB,OAAO,sBAAWF,MAAME,KAAjB,EAAwByB,aAAazB,KAArC,CAAT,EAHa,EAAlB;AAKA,MAAM4B,cAAc,IAAIC,SAAJ,GAAgBC,eAAhB,CAClBH,SADkB,EAElB,eAFkB,CAApB;AAIA,MAAMI,aAAaH,YAAYI,aAAZ,CAA0B,KAA1B,CAAnB;;AAEAR,UAAQS,UAAR,CAAmBC,YAAnB,CAAgCH,UAAhC,EAA4CP,OAA5C;AACD;;AAED;;;;;AAKA,SAASE,QAAT,CAAkBF,OAAlB,EAA2B;AACzB,SAAOJ,MAAMC,IAAN,CAAWG,QAAQW,UAAnB,EAA+B1B,MAA/B,CAAsC,UAACX,KAAD,EAAQsC,IAAR,EAAiB;AAC5DtC,UAAMsC,KAAKzC,IAAX,IAAmByC,KAAKC,KAAxB;AACA,WAAOvC,KAAP;AACD,GAHM,EAGJ,EAHI,CAAP;AAID;;kBAEciB,O;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Df;;;;;;AAEA;;;;;;;AAOA,SAASD,KAAT,CAAenB,IAAf,EAAiC;AAAA,MAAZG,KAAY,uEAAJ,EAAI;;AAC/BwC,UAAQC,IAAR,CACE,gFADF;;AAIA,MAAI,CAAC5C,IAAL,EAAW;AACT,UAAM,IAAIsB,KAAJ,CAAU,sDAAV,CAAN;AACD;;AAED,MAAI,CAACT,gBAAMb,IAAN,CAAL,EAAkB;AAChB,UAAM,IAAIsB,KAAJ,yBAEFtB,IAFE,oEAAN;AAKD;;AAED,SAAOa,gBAAMb,IAAN,EAAYmB,KAAZ,CAAkBhB,KAAlB,CAAP;AACD;;kBAEcgB,K", "file": "feather.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"feather\"] = factory();\n\telse\n\t\troot[\"feather\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "/*!\n  Copyright (c) 2016 <PERSON>.\n  Licensed under the MIT License (MIT), see\n  http://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar classNames = (function () {\n\t\t// don't inherit from Object so we can skip hasOwnProperty check later\n\t\t// http://stackoverflow.com/questions/15518328/creating-js-object-with-object-createnull#answer-21079232\n\t\tfunction StorageObject() {}\n\t\tStorageObject.prototype = Object.create(null);\n\n\t\tfunction _parseArray (resultSet, array) {\n\t\t\tvar length = array.length;\n\n\t\t\tfor (var i = 0; i < length; ++i) {\n\t\t\t\t_parse(resultSet, array[i]);\n\t\t\t}\n\t\t}\n\n\t\tvar hasOwn = {}.hasOwnProperty;\n\n\t\tfunction _parseNumber (resultSet, num) {\n\t\t\tresultSet[num] = true;\n\t\t}\n\n\t\tfunction _parseObject (resultSet, object) {\n\t\t\tfor (var k in object) {\n\t\t\t\tif (hasOwn.call(object, k)) {\n\t\t\t\t\t// set value to false instead of deleting it to avoid changing object structure\n\t\t\t\t\t// https://www.smashingmagazine.com/2012/11/writing-fast-memory-efficient-javascript/#de-referencing-misconceptions\n\t\t\t\t\tresultSet[k] = !!object[k];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tvar SPACE = /\\s+/;\n\t\tfunction _parseString (resultSet, str) {\n\t\t\tvar array = str.split(SPACE);\n\t\t\tvar length = array.length;\n\n\t\t\tfor (var i = 0; i < length; ++i) {\n\t\t\t\tresultSet[array[i]] = true;\n\t\t\t}\n\t\t}\n\n\t\tfunction _parse (resultSet, arg) {\n\t\t\tif (!arg) return;\n\t\t\tvar argType = typeof arg;\n\n\t\t\t// 'foo bar'\n\t\t\tif (argType === 'string') {\n\t\t\t\t_parseString(resultSet, arg);\n\n\t\t\t// ['foo', 'bar', ...]\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\t_parseArray(resultSet, arg);\n\n\t\t\t// { 'foo': true, ... }\n\t\t\t} else if (argType === 'object') {\n\t\t\t\t_parseObject(resultSet, arg);\n\n\t\t\t// '130'\n\t\t\t} else if (argType === 'number') {\n\t\t\t\t_parseNumber(resultSet, arg);\n\t\t\t}\n\t\t}\n\n\t\tfunction _classNames () {\n\t\t\t// don't leak arguments\n\t\t\t// https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments\n\t\t\tvar len = arguments.length;\n\t\t\tvar args = Array(len);\n\t\t\tfor (var i = 0; i < len; i++) {\n\t\t\t\targs[i] = arguments[i];\n\t\t\t}\n\n\t\t\tvar classSet = new StorageObject();\n\t\t\t_parseArray(classSet, args);\n\n\t\t\tvar list = [];\n\n\t\t\tfor (var k in classSet) {\n\t\t\t\tif (classSet[k]) {\n\t\t\t\t\tlist.push(k)\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn list.join(' ');\n\t\t}\n\n\t\treturn _classNames;\n\t})();\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "require('../../modules/es.string.iterator');\nrequire('../../modules/es.array.from');\nvar path = require('../../internals/path');\n\nmodule.exports = path.Array.from;\n", "module.exports = function (it) {\n  if (typeof it != 'function') {\n    throw TypeError(String(it) + ' is not a function');\n  } return it;\n};\n", "var isObject = require('../internals/is-object');\n\nmodule.exports = function (it) {\n  if (!isObject(it)) {\n    throw TypeError(String(it) + ' is not an object');\n  } return it;\n};\n", "'use strict';\nvar bind = require('../internals/bind-context');\nvar toObject = require('../internals/to-object');\nvar callWithSafeIterationClosing = require('../internals/call-with-safe-iteration-closing');\nvar isArrayIteratorMethod = require('../internals/is-array-iterator-method');\nvar toLength = require('../internals/to-length');\nvar createProperty = require('../internals/create-property');\nvar getIteratorMethod = require('../internals/get-iterator-method');\n\n// `Array.from` method\n// https://tc39.github.io/ecma262/#sec-array.from\nmodule.exports = function from(arrayLike /* , mapfn = undefined, thisArg = undefined */) {\n  var O = toObject(arrayLike);\n  var C = typeof this == 'function' ? this : Array;\n  var argumentsLength = arguments.length;\n  var mapfn = argumentsLength > 1 ? arguments[1] : undefined;\n  var mapping = mapfn !== undefined;\n  var index = 0;\n  var iteratorMethod = getIteratorMethod(O);\n  var length, result, step, iterator;\n  if (mapping) mapfn = bind(mapfn, argumentsLength > 2 ? arguments[2] : undefined, 2);\n  // if the target is not iterable or it's an array with the default iterator - use a simple case\n  if (iteratorMethod != undefined && !(C == Array && isArrayIteratorMethod(iteratorMethod))) {\n    iterator = iteratorMethod.call(O);\n    result = new C();\n    for (;!(step = iterator.next()).done; index++) {\n      createProperty(result, index, mapping\n        ? callWithSafeIterationClosing(iterator, mapfn, [step.value, index], true)\n        : step.value\n      );\n    }\n  } else {\n    length = toLength(O.length);\n    result = new C(length);\n    for (;length > index; index++) {\n      createProperty(result, index, mapping ? mapfn(O[index], index) : O[index]);\n    }\n  }\n  result.length = index;\n  return result;\n};\n", "var toIndexedObject = require('../internals/to-indexed-object');\nvar toLength = require('../internals/to-length');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\n// false -> Array#indexOf\n// https://tc39.github.io/ecma262/#sec-array.prototype.indexof\n// true  -> Array#includes\n// https://tc39.github.io/ecma262/#sec-array.prototype.includes\nmodule.exports = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = toLength(O.length);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) if (IS_INCLUDES || index in O) {\n      if (O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n", "var aFunction = require('../internals/a-function');\n\n// optional / simple context binding\nmodule.exports = function (fn, that, length) {\n  aFunction(fn);\n  if (that === undefined) return fn;\n  switch (length) {\n    case 0: return function () {\n      return fn.call(that);\n    };\n    case 1: return function (a) {\n      return fn.call(that, a);\n    };\n    case 2: return function (a, b) {\n      return fn.call(that, a, b);\n    };\n    case 3: return function (a, b, c) {\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n", "var anObject = require('../internals/an-object');\n\n// call something on iterator step with safe closing on error\nmodule.exports = function (iterator, fn, value, ENTRIES) {\n  try {\n    return ENTRIES ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch (error) {\n    var returnMethod = iterator['return'];\n    if (returnMethod !== undefined) anObject(returnMethod.call(iterator));\n    throw error;\n  }\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var called = 0;\n  var iteratorWithReturn = {\n    next: function () {\n      return { done: !!called++ };\n    },\n    'return': function () {\n      SAFE_CLOSING = true;\n    }\n  };\n  iteratorWithReturn[ITERATOR] = function () {\n    return this;\n  };\n  // eslint-disable-next-line no-throw-literal\n  Array.from(iteratorWithReturn, function () { throw 2; });\n} catch (error) { /* empty */ }\n\nmodule.exports = function (exec, SKIP_CLOSING) {\n  if (!SKIP_CLOSING && !SAFE_CLOSING) return false;\n  var ITERATION_SUPPORT = false;\n  try {\n    var object = {};\n    object[ITERATOR] = function () {\n      return {\n        next: function () {\n          return { done: ITERATION_SUPPORT = true };\n        }\n      };\n    };\n    exec(object);\n  } catch (error) { /* empty */ }\n  return ITERATION_SUPPORT;\n};\n", "var toString = {}.toString;\n\nmodule.exports = function (it) {\n  return toString.call(it).slice(8, -1);\n};\n", "var classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : result;\n};\n", "var has = require('../internals/has');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!has(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n  }\n};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "'use strict';\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(1, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = function (object, key, value) {\n  var propertyKey = toPrimitive(key);\n  if (propertyKey in object) definePropertyModule.f(object, propertyKey, createPropertyDescriptor(0, value));\n  else object[propertyKey] = value;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar createIteratorConstructor = require('../internals/create-iterator-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar hide = require('../internals/hide');\nvar redefine = require('../internals/redefine');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\n\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND in IterablePrototype) return IterablePrototype[KIND];\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    } return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (IteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (typeof CurrentIteratorPrototype[ITERATOR] != 'function') {\n          hide(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if (DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    INCORRECT_VALUES_NAME = true;\n    defaultIterator = function values() { return nativeIterator.call(this); };\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    hide(IterablePrototype, ITERATOR, defaultIterator);\n  }\n  Iterators[NAME] = defaultIterator;\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        redefine(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  return methods;\n};\n", "var fails = require('../internals/fails');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !fails(function () {\n  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;\n});\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar exist = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return exist ? document.createElement(it) : {};\n};\n", "// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "var global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar hide = require('../internals/hide');\nvar redefine = require('../internals/redefine');\nvar setGlobal = require('../internals/set-global');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target      - name of the target object\n  options.global      - target is the global object\n  options.stat        - export as static methods of target\n  options.proto       - export as prototype methods of target\n  options.real        - real prototype method for the `pure` version\n  options.forced      - export even if the native feature is available\n  options.bind        - bind methods to the target, required for the `pure` version\n  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n  options.sham        - add a flag to not completely full polyfills\n  options.enumerable  - export as enumerable property\n  options.noTargetGet - prevent calling a getter on target\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || setGlobal(TARGET, {});\n  } else {\n    target = (global[TARGET] || {}).prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.noTargetGet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty === typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      hide(sourceProperty, 'sham', true);\n    }\n    // extend global\n    redefine(target, key, sourceProperty, options);\n  }\n};\n", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "var shared = require('../internals/shared');\n\nmodule.exports = shared('native-function-to-string', Function.toString);\n", "var classof = require('../internals/classof');\nvar Iterators = require('../internals/iterators');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = function (it) {\n  if (it != undefined) return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n", "var O = 'object';\nvar check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line no-undef\n  check(typeof globalThis == O && globalThis) ||\n  check(typeof window == O && window) ||\n  check(typeof self == O && self) ||\n  check(typeof global == O && global) ||\n  // eslint-disable-next-line no-new-func\n  Function('return this')();\n", "var hasOwnProperty = {}.hasOwnProperty;\n\nmodule.exports = function (it, key) {\n  return hasOwnProperty.call(it, key);\n};\n", "module.exports = {};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "var global = require('../internals/global');\n\nvar document = global.document;\n\nmodule.exports = document && document.documentElement;\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n", "// fallback for non-array-like ES3 and non-enumerable old V8 strings\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar split = ''.split;\n\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split.call(it, '') : Object(it);\n} : Object;\n", "var NATIVE_WEAK_MAP = require('../internals/native-weak-map');\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar hide = require('../internals/hide');\nvar objectHas = require('../internals/has');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP) {\n  var store = new WeakMap();\n  var wmget = store.get;\n  var wmhas = store.has;\n  var wmset = store.set;\n  set = function (it, metadata) {\n    wmset.call(store, it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return wmget.call(store, it) || {};\n  };\n  has = function (it) {\n    return wmhas.call(store, it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    hide(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return objectHas(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return objectHas(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayPrototype = Array.prototype;\n\n// check on default Array iterator\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);\n};\n", "var fails = require('../internals/fails');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : typeof detection == 'function' ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "module.exports = function (it) {\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n", "module.exports = false;\n", "'use strict';\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar hide = require('../internals/hide');\nvar has = require('../internals/has');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\nvar returnThis = function () { return this; };\n\n// `%IteratorPrototype%` object\n// https://tc39.github.io/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nif (IteratorPrototype == undefined) IteratorPrototype = {};\n\n// 25.1.2.1.1 %IteratorPrototype%[@@iterator]()\nif (!IS_PURE && !has(IteratorPrototype, ITERATOR)) hide(IteratorPrototype, ITERATOR, returnThis);\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n", "module.exports = {};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  // Chrome 38 Symbol has incorrect toString conversion\n  // eslint-disable-next-line no-undef\n  return !String(Symbol());\n});\n", "var global = require('../internals/global');\nvar nativeFunctionToString = require('../internals/function-to-string');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = typeof WeakMap === 'function' && /native code/.test(nativeFunctionToString.call(WeakMap));\n", "var anObject = require('../internals/an-object');\nvar defineProperties = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar PROTOTYPE = 'prototype';\nvar Empty = function () { /* empty */ };\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var length = enumBugKeys.length;\n  var lt = '<';\n  var script = 'script';\n  var gt = '>';\n  var js = 'java' + script + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  iframe.src = String(js);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + script + gt + 'document.F=Object' + lt + '/' + script + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while (length--) delete createDict[PROTOTYPE][enumBugKeys[length]];\n  return createDict();\n};\n\n// ******** / ******** Object.create(O [, Properties])\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty();\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : defineProperties(result, Properties);\n};\n\nhiddenKeys[IE_PROTO] = true;\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar objectKeys = require('../internals/object-keys');\n\nmodule.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var i = 0;\n  var key;\n  while (length > i) definePropertyModule.f(O, key = keys[i++], Properties[key]);\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar anObject = require('../internals/an-object');\nvar toPrimitive = require('../internals/to-primitive');\n\nvar nativeDefineProperty = Object.defineProperty;\n\nexports.f = DESCRIPTORS ? nativeDefineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return nativeDefineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPrimitive = require('../internals/to-primitive');\nvar has = require('../internals/has');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\nvar nativeGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\nexports.f = DESCRIPTORS ? nativeGetOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPrimitive(P, true);\n  if (IE8_DOM_DEFINE) try {\n    return nativeGetOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (has(O, P)) return createPropertyDescriptor(!propertyIsEnumerableModule.f.call(O, P), O[P]);\n};\n", "// ******** / ******** Object.getOwnPropertyNames(O)\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "exports.f = Object.getOwnPropertySymbols;\n", "var has = require('../internals/has');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar ObjectPrototype = Object.prototype;\n\n// ******** / ******** Object.getPrototypeOf(O)\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? Object.getPrototypeOf : function (O) {\n  O = toObject(O);\n  if (has(O, IE_PROTO)) return O[IE_PROTO];\n  if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectPrototype : null;\n};\n", "var has = require('../internals/has');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar arrayIncludes = require('../internals/array-includes');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar arrayIndexOf = arrayIncludes(false);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !has(hiddenKeys, key) && has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (has(O, key = names[i++])) {\n    ~arrayIndexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// ********* / ********* Object.keys(O)\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n", "'use strict';\nvar nativePropertyIsEnumerable = {}.propertyIsEnumerable;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !nativePropertyIsEnumerable.call({ 1: 2 }, 1);\n\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : nativePropertyIsEnumerable;\n", "var validateSetPrototypeOfArguments = require('../internals/validate-set-prototype-of-arguments');\n\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var correctSetter = false;\n  var test = {};\n  var setter;\n  try {\n    setter = Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set;\n    setter.call(test, []);\n    correctSetter = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    validateSetPrototypeOfArguments(O, proto);\n    if (correctSetter) setter.call(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "var global = require('../internals/global');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar Reflect = global.Reflect;\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = Reflect && Reflect.ownKeys || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? keys.concat(getOwnPropertySymbols(it)) : keys;\n};\n", "module.exports = require('../internals/global');\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hide = require('../internals/hide');\nvar has = require('../internals/has');\nvar setGlobal = require('../internals/set-global');\nvar nativeFunctionToString = require('../internals/function-to-string');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar getInternalState = InternalStateModule.get;\nvar enforceInternalState = InternalStateModule.enforce;\nvar TEMPLATE = String(nativeFunctionToString).split('toString');\n\nshared('inspectSource', function (it) {\n  return nativeFunctionToString.call(it);\n});\n\n(module.exports = function (O, key, value, options) {\n  var unsafe = options ? !!options.unsafe : false;\n  var simple = options ? !!options.enumerable : false;\n  var noTargetGet = options ? !!options.noTargetGet : false;\n  if (typeof value == 'function') {\n    if (typeof key == 'string' && !has(value, 'name')) hide(value, 'name', key);\n    enforceInternalState(value).source = TEMPLATE.join(typeof key == 'string' ? key : '');\n  }\n  if (O === global) {\n    if (simple) O[key] = value;\n    else setGlobal(key, value);\n    return;\n  } else if (!unsafe) {\n    delete O[key];\n  } else if (!noTargetGet && O[key]) {\n    simple = true;\n  }\n  if (simple) O[key] = value;\n  else hide(O, key, value);\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, 'toString', function toString() {\n  return typeof this == 'function' && getInternalState(this).source || nativeFunctionToString.call(this);\n});\n", "// `RequireObjectCoercible` abstract operation\n// https://tc39.github.io/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "var global = require('../internals/global');\nvar hide = require('../internals/hide');\n\nmodule.exports = function (key, value) {\n  try {\n    hide(global, key, value);\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "var defineProperty = require('../internals/object-define-property').f;\nvar has = require('../internals/has');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (it, TAG, STATIC) {\n  if (it && !has(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {\n    defineProperty(it, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "var shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\nvar IS_PURE = require('../internals/is-pure');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.1.3',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2019 <PERSON> (zloirock.ru)'\n});\n", "var toInteger = require('../internals/to-integer');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\n// CONVERT_TO_STRING: true  -> String#at\n// CONVERT_TO_STRING: false -> String#codePointAt\nmodule.exports = function (that, pos, CONVERT_TO_STRING) {\n  var S = String(requireObjectCoercible(that));\n  var position = toInteger(pos);\n  var size = S.length;\n  var first, second;\n  if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n  first = S.charCodeAt(position);\n  return first < 0xD800 || first > 0xDBFF || position + 1 === size\n    || (second = S.charCodeAt(position + 1)) < 0xDC00 || second > 0xDFFF\n      ? CONVERT_TO_STRING ? S.charAt(position) : first\n      : CONVERT_TO_STRING ? S.slice(position, position + 2) : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n};\n", "var toInteger = require('../internals/to-integer');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(length, length).\nmodule.exports = function (index, length) {\n  var integer = toInteger(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "var ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `ToInteger` abstract operation\n// https://tc39.github.io/ecma262/#sec-tointeger\nmodule.exports = function (argument) {\n  return isNaN(argument = +argument) ? 0 : (argument > 0 ? floor : ceil)(argument);\n};\n", "var toInteger = require('../internals/to-integer');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.github.io/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  return argument > 0 ? min(toInteger(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "var requireObjectCoercible = require('../internals/require-object-coercible');\n\n// `ToObject` abstract operation\n// https://tc39.github.io/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n", "var isObject = require('../internals/is-object');\n\n// 7.1.1 ToPrimitive(input [, PreferredType])\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function (it, S) {\n  if (!isObject(it)) return it;\n  var fn, val;\n  if (S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (typeof (fn = it.valueOf) == 'function' && !isObject(val = fn.call(it))) return val;\n  if (!S && typeof (fn = it.toString) == 'function' && !isObject(val = fn.call(it))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n", "var id = 0;\nvar postfix = Math.random();\n\nmodule.exports = function (key) {\n  return 'Symbol('.concat(key === undefined ? '' : key, ')_', (++id + postfix).toString(36));\n};\n", "var isObject = require('../internals/is-object');\nvar anObject = require('../internals/an-object');\n\nmodule.exports = function (O, proto) {\n  anObject(O);\n  if (!isObject(proto) && proto !== null) {\n    throw TypeError(\"Can't set \" + String(proto) + ' as a prototype');\n  }\n};\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nvar Symbol = global.Symbol;\nvar store = shared('wks');\n\nmodule.exports = function (name) {\n  return store[name] || (store[name] = NATIVE_SYMBOL && Symbol[name]\n    || (NATIVE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n", "var $ = require('../internals/export');\nvar from = require('../internals/array-from');\nvar checkCorrectnessOfIteration = require('../internals/check-correctness-of-iteration');\n\nvar INCORRECT_ITERATION = !checkCorrectnessOfIteration(function (iterable) {\n  Array.from(iterable);\n});\n\n// `Array.from` method\n// https://tc39.github.io/ecma262/#sec-array.from\n$({ target: 'Array', stat: true, forced: INCORRECT_ITERATION }, {\n  from: from\n});\n", "'use strict';\nvar codePointAt = require('../internals/string-at');\nvar InternalStateModule = require('../internals/internal-state');\nvar defineIterator = require('../internals/define-iterator');\n\nvar STRING_ITERATOR = 'String Iterator';\nvar setInternalState = InternalStateModule.set;\nvar getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);\n\n// `String.prototype[@@iterator]` method\n// https://tc39.github.io/ecma262/#sec-string.prototype-@@iterator\ndefineIterator(String, 'String', function (iterated) {\n  setInternalState(this, {\n    type: STRING_ITERATOR,\n    string: String(iterated),\n    index: 0\n  });\n// `%StringIteratorPrototype%.next` method\n// https://tc39.github.io/ecma262/#sec-%stringiteratorprototype%.next\n}, function next() {\n  var state = getInternalState(this);\n  var string = state.string;\n  var index = state.index;\n  var point;\n  if (index >= string.length) return { value: undefined, done: true };\n  point = codePointAt(string, index, true);\n  state.index += point.length;\n  return { value: point, done: false };\n});\n", "var g;\r\n\r\n// This works in non-strict mode\r\ng = (function() {\r\n\treturn this;\r\n})();\r\n\r\ntry {\r\n\t// This works if eval is allowed (see CSP)\r\n\tg = g || Function(\"return this\")() || (1, eval)(\"this\");\r\n} catch (e) {\r\n\t// This works if the window reference is available\r\n\tif (typeof window === \"object\") g = window;\r\n}\r\n\r\n// g can still be undefined, but nothing to do about it...\r\n// We return undefined, instead of nothing here, so it's\r\n// easier to handle this case. if(!global) { ...}\r\n\r\nmodule.exports = g;\r\n", "import classnames from 'classnames/dedupe';\n\nimport DEFAULT_ATTRS from './default-attrs.json';\n\nclass Icon {\n  constructor(name, contents, tags = []) {\n    this.name = name;\n    this.contents = contents;\n    this.tags = tags;\n    this.attrs = {\n      ...DEFAULT_ATTRS,\n      ...{ class: `feather feather-${name}` },\n    };\n  }\n\n  /**\n   * Create an SVG string.\n   * @param {Object} attrs\n   * @returns {string}\n   */\n  toSvg(attrs = {}) {\n    const combinedAttrs = {\n      ...this.attrs,\n      ...attrs,\n      ...{ class: classnames(this.attrs.class, attrs.class) },\n    };\n\n    return `<svg ${attrsToString(combinedAttrs)}>${this.contents}</svg>`;\n  }\n\n  /**\n   * Return string representation of an `Icon`.\n   *\n   * Added for backward compatibility. If old code expects `feather.icons.<name>`\n   * to be a string, `toString()` will get implicitly called.\n   *\n   * @returns {string}\n   */\n  toString() {\n    return this.contents;\n  }\n}\n\n/**\n * Convert attributes object to string of HTML attributes.\n * @param {Object} attrs\n * @returns {string}\n */\nfunction attrsToString(attrs) {\n  return Object.keys(attrs)\n    .map(key => `${key}=\"${attrs[key]}\"`)\n    .join(' ');\n}\n\nexport default Icon;\n", "import Icon from './icon';\nimport icons from '../dist/icons.json';\nimport tags from './tags.json';\n\nexport default Object.keys(icons)\n  .map(key => new Icon(key, icons[key], tags[key]))\n  .reduce((object, icon) => {\n    object[icon.name] = icon;\n    return object;\n  }, {});\n", "import icons from './icons';\nimport toSvg from './to-svg';\nimport replace from './replace';\n\nmodule.exports = { icons, toSvg, replace };\n", "/* eslint-env browser */\nimport classnames from 'classnames/dedupe';\n\nimport icons from './icons';\n\n/**\n * Replace all HTML elements that have a `data-feather` attribute with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {Object} attrs\n */\nfunction replace(attrs = {}) {\n  if (typeof document === 'undefined') {\n    throw new Error('`feather.replace()` only works in a browser environment.');\n  }\n\n  const elementsToReplace = document.querySelectorAll('[data-feather]');\n\n  Array.from(elementsToReplace).forEach(element =>\n    replaceElement(element, attrs),\n  );\n}\n\n/**\n * Replace a single HTML element with SVG markup\n * corresponding to the element's `data-feather` attribute value.\n * @param {HTMLElement} element\n * @param {Object} attrs\n */\nfunction replaceElement(element, attrs = {}) {\n  const elementAttrs = getAttrs(element);\n  const name = elementAttrs['data-feather'];\n  delete elementAttrs['data-feather'];\n\n  const svgString = icons[name].toSvg({\n    ...attrs,\n    ...elementAttrs,\n    ...{ class: classnames(attrs.class, elementAttrs.class) },\n  });\n  const svgDocument = new DOMParser().parseFromString(\n    svgString,\n    'image/svg+xml',\n  );\n  const svgElement = svgDocument.querySelector('svg');\n\n  element.parentNode.replaceChild(svgElement, element);\n}\n\n/**\n * Get the attributes of an HTML element.\n * @param {HTMLElement} element\n * @returns {Object}\n */\nfunction getAttrs(element) {\n  return Array.from(element.attributes).reduce((attrs, attr) => {\n    attrs[attr.name] = attr.value;\n    return attrs;\n  }, {});\n}\n\nexport default replace;\n", "import icons from './icons';\n\n/**\n * Create an SVG string.\n * @deprecated\n * @param {string} name\n * @param {Object} attrs\n * @returns {string}\n */\nfunction toSvg(name, attrs = {}) {\n  console.warn(\n    'feather.toSvg() is deprecated. Please use feather.icons[name].toSvg() instead.',\n  );\n\n  if (!name) {\n    throw new Error('The required `key` (icon name) parameter is missing.');\n  }\n\n  if (!icons[name]) {\n    throw new Error(\n      `No icon matching '${\n        name\n      }'. See the complete list of icons at https://feathericons.com`,\n    );\n  }\n\n  return icons[name].toSvg(attrs);\n}\n\nexport default toSvg;\n"], "sourceRoot": ""}
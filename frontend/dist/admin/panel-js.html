<script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
<script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
<script>
    var readFromStorage = function (key) {
        if (!window.Storage) {
            // From: https://stackoverflow.com/a/15724300/2367037
            var value = '; ' + document.cookie;
            var parts = value.split('; ' + key + '=');
            if (parts.length === 2) {
                return parts.pop().split(';').shift();
            }
        } else {
            return window.localStorage.getItem(key);
        }
    };
    var writeToStorage = function (key, value) {
        if (window.Storage) {
            window.localStorage.setItem(key, value);
        }

        var expireDays = 365;
        var expiresDate = new Date();
        expiresDate.setDate(expiresDate.getDate() + expireDays);
        document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
    };
    var deleteFromStorage = function (key) {
        if (window.Storage) {
            window.localStorage.removeItem(key);
        }

        // Delete cookie by setting it to expire in the past
        document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
    };
</script>
<script src="/assets/js/formio/formio.full.js"></script>

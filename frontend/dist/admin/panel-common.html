<div id="admin-panel"></div>
<script>
    function setWidth() {
        const newWidth = window.localStorage.getItem('admin-panel|newWidth');
        if (newWidth) {
            offcanvas.style.width = `${newWidth}px`;
            $('.offcanvas-push').css('margin-right', newWidth + 'px');
        }
    }
    function togglePanelOn(e) {
        console.log(`togglePanelOn`);
        if (!e) {
            return;
        }
        $('body').addClass('offcanvas-push');
        $('#show-panel-btn').addClass('offcanvas-push');
        setWidth();
        $('#show-panel-btn')
            .off('click')
            .on('click', function (event) {
                if ($('#offcanvas')) {
                    $('#offcanvas').offcanvas('hide');
                } else {
                    togglePanelOff(event);
                }
            });
    }
    function togglePanelOff(e) {
        console.log(`togglePanelOff: ${e}`);
        if (!e) {
            return;
        }
        $('.offcanvas-push').css('margin-right', '');
        $('body').removeClass('offcanvas-push');
        $('#show-panel-btn').removeClass('offcanvas-push');
        $('.admin-panel-spinner').hide();
        $('#show-panel-btn').off('click').on('click', panelFuncE);
        $(document).off('keydown.panelNav');
    }
    function highlightRow(opk) {
        $('.highlighted-row').removeClass('highlighted-row');
        $(`[data-opk="${opk}"]`).closest('tr').addClass('highlighted-row');
        currentRowId = opk;
    }
    function removeHighlight() {
        $('.highlighted-row').removeClass('highlighted-row');
        currentRowId = null;
    }
    function getAdjacentRowId(direction) {
        const rows = $('tr:has(.panel-btn)').toArray();
        const currentIndex = rows.findIndex((row) => $(row).find('.panel-btn').data('opk') == currentRowId);
        const adjacentIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
        if (adjacentIndex >= 0 && adjacentIndex < rows.length) {
            return $(rows[adjacentIndex]).find('.panel-btn').data('opk');
        }
        return null;
    }
    function navigatePanel(direction) {
        const adjacentRowId = getAdjacentRowId(direction);
        if (adjacentRowId) {
            const model = $(`[data-opk="${adjacentRowId}"]`).data('model');
            panelFunc(model, adjacentRowId);
        }
    }
    function panelFuncE(event) {
        var opk = $(event.target).data('opk');
        var model = $(event.target).data('model');
        return panelFunc(model, opk);
    }
    function panelFunc(model, opk) {
        var url = `/app/panel-html/${model}/${opk}/`;
        $('#show-panel-btn').prop('disabled', true);
        $('.admin-panel-spinner').show();
        var setupPanel = function () {
            $('.admin-panel-spinner').hide();
            $('#show-panel-btn').prop('disabled', false);
            $('#offcanvas').on('show.bs.offcanvas', function (event) {
                togglePanelOn(event);
                highlightRow(opk);
            });
            $('#offcanvas').offcanvas('show');
            $('#offcanvas [data-bs-toggle="popover"]').each(function (index, element) {
                const popover = new bootstrap.Popover(element, { trigger: 'focus' });
            });
            $('#offcanvas').on('hide.bs.offcanvas', function (event) {
                togglePanelOff(event);
                removeHighlight();
            });
            $('#offcanvas .resize-handle').on('mousedown', function (e) {
                //- console.log('mousedown');
                isResizing = true;
                lastDownX = e.clientX;
                $('body').addClass('resizing');
            });
            document.addEventListener('mousemove', function (e) {
                if (!isResizing) return;
                const currentWidth = offcanvas.getBoundingClientRect().width;
                const widthChange = lastClientX - e.clientX;
                lastClientX = e.clientX;
                const newWidth = currentWidth + widthChange;
                window.localStorage.setItem('admin-panel|newWidth', newWidth);
                setWidth();
                //- offcanvas.style.width = `${newWidth}px`;
                //- $('.offcanvas-push').css('margin-right', newWidth + 'px');
                //- console.log(`mousemove: currentWidth:${currentWidth} widthChange:${widthChange} newWidth:${newWidth} lastClientX:${lastClientX}`);
            });
            document.addEventListener('mouseup', function (e) {
                //- console.log('mouseup');
                isResizing = false;
                document.body.classList.remove('resizing');
            });
            // navigation buttons
            $('.panel-nav-up')
                .off('click')
                .on('click', function () {
                    navigatePanel('up');
                });
            $('.panel-nav-down')
                .off('click')
                .on('click', function () {
                    navigatePanel('down');
                });
            // Add keyboard navigation
            /*
    $(document).off('keydown.panelNav').on('keydown.panelNav', function(e) {
        if ($('#offcanvas').hasClass('show')) {
            if (e.key === 'k' || e.key === 'K') {
                navigatePanel('up');
                e.preventDefault();
            } else if (e.key === 'j' || e.key === 'J') {
                navigatePanel('down');
                e.preventDefault();
            }
        }
    });
    //*/
        };
        // AJAX
        $.get({
            url: url,
            success: function (html) {
                $('#admin-panel').html(html);
                setupPanel();
            },
        });
    }
    $('#show-panel-btn').off('click').on('click', panelFuncE);
    $('.panel-btn').off('click').on('click', panelFuncE);
    let isResizing = false;
    let lastDownX;
    let lastClientX;
    let currentRowId = null;
</script>

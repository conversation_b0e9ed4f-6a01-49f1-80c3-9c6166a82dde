<!-- FORM START-->
<!-- {% for field in form %}{% if field.is_hidden %}-->
{{field}}
<!-- {% else %}-->
<div class="row gx-3">
    <div class="col-md-10 mb-3">
        <!-- {% if field.field.widget.input_type == 'checkbox' %}-->
        <div class="form-check">
            <label class="form-check-label" for="{{field.id_for_label}}">{{field.label}}</label>
            {{field}}
        </div>
        <!-- {% else %}-->
        <label class="small mb-1" for="{{field.id_for_label}}">{{field.label}}</label>
        {{field}}
        <!-- {% if field.help_text %}-->
        <div class="text-xs">{{field.help_text}}</div>
        <!-- {% endif %}-->
        <!-- {% endif %}-->
        <div class="text-danger">{{field.errors}}</div>
    </div>
</div>
<!-- {% endif %}{% endfor %}-->
<!-- {% if form.non_field_errors %}{% for error in form.non_field_errors %}-->
<div class="text-danger">{{ error }}</div>
<!-- {% endfor %}{% endif %}-->
<!-- FORM END-->

<!-- {% load template_extras %}-->
<div class="modal fade" id="rx-modal" data-bs-scroll="true" data-bs-backdrop="true" tabindex="-1">
    <div class="modal-dialog modal-xl shadow-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Rx: {{patient.name}} ({{patient.birthday}})</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" id="rxt-form">
                    {% csrf_token %}
                    <!-- {% with q_rxt_keys=questionnaire.rxt_keys %}-->
                    <!-- {% for category, rxts in cat_rxts.items %}-->
                    <strong>{{category}}</strong>
                    <hr class="my-0" />
                    <div class="row mb-3">
                        <!-- {% for t in rxts %}-->
                        <div class="col-md-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" id="rxt_{{t.key}}" type="checkbox" name="rxts" value="{{t.key}}" {% if t.key in q_rxt_keys %}checked{% endif %} />
                                <label class="form-check-label" for="rxt_{{t.key}}">
                                    {{t.get_modal_label}}
                                    <br />
                                    <span class="text-xs text-muted">{{t.key}}</span>
                                </label>
                            </div>
                        </div>
                        <!-- {% endfor %}-->
                    </div>
                    <!-- {% endfor %}-->
                    <!-- {% endwith %}-->
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger-soft text-danger" type="button" data-bs-dismiss="modal">Cancel</button>
                <button class="btn btn-primary-soft text-primary save-btn" type="button" id="rxt">
                    Save
                    <i class="fa fa-refresh fa-spin button-icon-right ms-2" id="rxt-spinner" style="display: none"></i>
                </button>
                <button class="btn btn-primary-soft text-primary save-btn" type="button" id="rxt-stamp">
                    Stamp &amp; Save
                    <i class="fa fa-refresh fa-spin button-icon-right ms-2" id="rxt-stamp-spinner" style="display: none"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="rq-modal" data-bs-scroll="true" data-bs-backdrop="true" tabindex="-1">
    <div class="modal-dialog modal-xl shadow-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Req: {{patient.name}} ({{patient.birthday}})</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" id="rq-form">
                    {% csrf_token %}
                    <!-- {% with q_assays=questionnaire.assay_choices.all %}-->
                    <!-- {% with ukeys=questionnaire.uninsured_keys %}-->
                    <!-- {% for category, assays in cat_assays.items %}-->
                    <strong>{{category}}</strong>
                    <hr class="my-0" />
                    <div class="row mb-3">
                        <!-- {% for a in assays %}-->
                        <div class="col-md-4 mb-2">
                            <label for="assay_{{a.key}}" ref="label">{{a.key}}: {{a.name|truncatechars:19}}</label>
                            <div class="small form-radio" role="radiogroup" ref="radioGroup">
                                <div class="form-check-inline" ref="wrapper">
                                    <label class="form-check-label label-position-right" for="assay_{{a.key}}-insured">
                                        <input class="form-check-input" type="radio" role="radio" value="insured" name="data.{{a.key}}" id="assay_{{a.key}}-insured" ref="input" {% if a in q_assays and a.key not in ukeys %}checked{% endif %} />
                                        <span>Insured</span>
                                    </label>
                                </div>
                                <div class="form-check-inline radio-selected" ref="wrapper">
                                    <label class="form-check-label label-position-right" for="assay_{{a.key}}-uninsured">
                                        <input class="form-check-input" type="radio" role="radio" value="uninsured" name="data.{{a.key}}" id="assay_{{a.key}}-uninsured" ref="input" {% if a in q_assays and a.key in ukeys %}checked{% endif %} />
                                        <span>Uninsured</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- {% endfor %}-->
                    </div>
                    <!-- {% endfor %}-->
                    <!-- {% endwith %}-->
                    <!-- {% endwith %}-->
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger-soft text-danger" type="button" data-bs-dismiss="modal">Cancel</button>
                <button class="btn btn-primary-soft text-primary save-btn" type="button" id="rq">
                    Save
                    <i class="fa fa-refresh fa-spin button-icon-right ms-2" id="rq-spinner" style="display: none"></i>
                </button>
                <button class="btn btn-primary-soft text-primary save-btn" type="button" id="rq-stamp">
                    Stamp &amp; Save
                    <i class="fa fa-refresh fa-spin button-icon-right ms-2" id="rq-stamp-spinner" style="display: none"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="mail-modal-new" data-bs-scroll="true" data-bs-backdrop="true" tabindex="-1">
    <div class="modal-dialog modal-xl shadow-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Message: {{patient.name}} ({{patient.birthday}})</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body"><div id="formio"></div></div>
        </div>
    </div>
</div>
<div class="modal fade" id="patient-modal" data-bs-scroll="true" data-bs-backdrop="true" tabindex="-1">
    <div class="modal-dialog modal-xl shadow-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Patient: {{patient.name}} ({{patient.birthday}})</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST" id="patient-form">{% csrf_token %} {{patient_form}}</form>
                <div class="row">
                    <div class="d-grid">
                        <div class="alert alert-success" id="patient-success" style="display: none">success</div>
                        <div class="alert alert-danger" id="patient-error" style="display: none">error</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger-soft text-danger" type="button" data-bs-dismiss="modal">Cancel</button>
                <button class="btn btn-primary-soft text-primary save-btn" type="button" id="patient">
                    Save
                    <i class="fa fa-refresh fa-spin button-icon-right ms-2" id="patient-spinner" style="display: none"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<div class="offcanvas offcanvas-responsive" id="offcanvas" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1">
    <div class="resize-handle"></div>
    <div class="offcanvas-header py-2 border-bottom">
        <div class="d-flex align-items-center">
            <!-- {% if model_name == 'labresultpage' or model_name == 'formio' %}-->
            <div class="panel-navigation">
                <button class="btn btn-outline-secondary btn-sm panel-nav-up" type="button">&uarr;</button>
                <button class="btn btn-outline-secondary btn-sm panel-nav-down" type="button">&darr;</button>
            </div>
            <!-- {% endif %}-->
            <div class="avatar-sq avatar-xl bg-white flex-shrink-0"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></div>
            <div class="ms-2">
                <!-- {% if patient %}-->
                <div class="panel-header-title">{{patient.name}} ({{patient.birthday}})</div>
                <!-- {% elif model_name == 'formio' and obj.kind == 'vca' %}-->
                <div class="panel-header-title">Anon VCA (Formio.id: {{obj.id}})</div>
                <!-- {% endif %}-->
                <div class="small">Model: {{model_name}} (id: {{obj.id}})</div>
                <div class="text-xs text-muted">{{questionnaire.product}}</div>
            </div>
        </div>
        <button class="btn-close" type="button" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body position-relative" style="overflow-y: scroll">
        <div class="sidenav">
            <!-- {% if patient %}-->
            <button class="btn btn-outline-gray-500 btn-icon position-absolute top-0 end-0 mt-1 me-1 border-0" type="button" data-bs-toggle="modal" data-bs-target="#patient-modal"><i class="fa fa-gear"></i></button>
            <h4>{{patient.age}} year old {{patient.human}}</h4>
            <!-- {% endif %} endif patient-->
            <!-- {% if model_name == 'room' %}-->
            <a href="{% url 'chat_llm_prompt' obj.user_doctor.id obj.id %}" target="_blank">LLM Prompt</a>
            <!-- {% endif %} endif room-->
            <!-- {% if questionnaire %}-->
            <strong class="small">Time Since Last Test:</strong>
            <ul>
                <!-- {% for q in questionnaire.last_test_freq %}-->
                <!-- {% if q.assay_keys %}-->
                <li>{{q.chat_completed|date:"Y-m-d"}}: {{q.assay_keys}} ({{q.days}} days) - {{q.intake_kind}}</li>
                <!-- {% endif %}{% if q.rxt_keys %}-->
                <li>{{q.chat_completed|date:"Y-m-d"}}: {{q.rxt_keys}} ({{q.days}} days) - {{q.intake_kind}}</li>
                <!-- {% endif %}-->
                <!-- {% endfor %}-->
            </ul>
            <!-- {% if questionnaire.lab %}-->
            <div class="small">
                <strong>Lab:</strong>
                <span>{{questionnaire.lab}}</span>
            </div>
            <div class="small">
                <strong>Address:</strong>
                <span>{{questionnaire.lab.address}}</span>
            </div>
            <!-- {% else %}-->
            <strong class="small text-orange-hard">Lab Missing</strong>
            <!-- {% endif %} end if lab-->
            <!-- {% if questionnaire.pharmacy %}-->
            <div class="small">
                <strong>Pharmacy:</strong>
                <span>{{questionnaire.pharmacy}}</span>
            </div>
            <div class="small">
                <strong>Address:</strong>
                <span>{{questionnaire.pharmacy.address}}</span>
            </div>
            <!-- {% else %}-->
            <strong class="small text-orange-hard">Pharmacy Missing</strong>
            <!-- {% endif %} endif pharmacy-->
            <!-- {% endif %} endif questionnaire-->
            <!-- {% if model_name == 'labresultpage' %}-->
            <iframe id="obj-preview" width="100%" height="50%"></iframe>
            <iframe id="pdf-preview" width="100%" height="50%"></iframe>
            <!-- {% endif %} endif questionnaire-->
            <div class="sidenav-menu">
                <div class="nav">
                    <!-- {% for obj_key, obj in objs.items %}-->
                    <a class="ps-0 nav-link" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#{{obj_key}}-collapse">
                        {{obj_key|format_formio_val}}
                        <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                    </a>
                    <div class="collapse show" id="{{obj_key}}-collapse">
                        <!-- {% if obj.forms %}-->
                        <button class="btn {% if obj.overrode %}btn-outline-red{% else %}btn-outline-blue{% endif %}" type="button" data-bs-toggle="modal" data-bs-target="#{{obj_key}}-modal">Edit Form</button>
                        <!-- {% endif %}-->
                        <!-- {% for s_key, components in obj.sections.items %}-->
                        <nav class="sidenav-menu-nested nav">
                            <a class="nav-link" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#{{obj_key}}{{s_key}}-collapse">
                                {{s_key|format_formio_val}}
                                <div class="sidenav-collapse-arrow"><i class="fas fa-angle-down"></i></div>
                            </a>
                            <div class="collapse show" id="{{obj_key}}{{s_key}}-collapse">
                                <nav class="sidenav-menu-nested nav">
                                    <!-- {% for c_key, c in components.items %}-->
                                    <!-- {% if c.tableView and not c.hidden and 'none of the above' not in c.question.lower and c.answer %}-->
                                    <div class="d-flex align-items-center">
                                        <a class="btn btn-transparent-dark btn-icon" data-bs-title="Q: {{c.question}}" data-bs-content="A: {{c.answer}}" data-bs-toggle="popover" data-bs-placement="left" style="width: 1.5rem !important; height: 1.5rem !important" role="button" tabindex="0"><i class="fa-solid fa-circle-info"></i></a>
                                        <div class="{% if c.adminFlag %}text-danger{% endif %}">{{c_key|format_formio_val}}: {{c.c_val|format_formio_val}}</div>
                                    </div>
                                    <!-- {% endif %} {% endfor%}-->
                                </nav>
                            </div>
                        </nav>
                        <!-- {% endfor %}-->
                    </div>
                    <!-- {% endfor %}-->
                </div>
            </div>
        </div>
    </div>
    <div class="row justify-content-center border-top py-3 g-0">
        <!-- {% if cat_assays %}-->
        <div class="col-lg-6 px-3">
            <div class="d-grid"><button class="btn btn-outline-blue" type="button" data-bs-toggle="modal" data-bs-target="#rq-modal">Edit Req</button></div>
        </div>
        <!-- {% endif %}-->
        <!-- {% if questionnaire.intake_kind == 'mail' %}-->
        <div class="col-lg-6 px-3">
            <div class="d-grid"><button class="btn btn-outline-blue" type="button" data-bs-toggle="modal" data-bs-target="#mail-modal-new">Preview Mail</button></div>
        </div>
        <!-- {% endif %}-->
        <!-- {% if cat_rxts %}-->
        <div class="col-lg-6 px-3">
            <div class="d-grid"><button class="btn btn-outline-blue" type="button" data-bs-toggle="modal" data-bs-target="#rx-modal">Edit Rx</button></div>
        </div>
        <!-- {% endif %}-->
    </div>
</div>
<script src="/assets/libs/lodash@4.17.15/lodash.min.js"></script>
<script>
    // {% if model_name == 'labresultpage' %}
    document.getElementById('pdf-preview').src = '{{obj.pdf_doc_url}}#toolbar=0&navpanes=0';
    document.getElementById('obj-preview').src = "{% url 'admin:website_labresultpage_custom_change' obj.pk %}";
    // {% endif %}
    //- draggable handle
    //- document.addEventListener('DOMContentLoaded', function() {
    //- });
    //- uncheckable radio button jquery plugin
    //- https://stackoverflow.com/questions/2117538/how-to-uncheck-a-radio-button
    (function ($) {
        $.fn.uncheckableRadio = function () {
            return this.each(function () {
                var radio = this,
                    label = $('label[for="' + radio.id + '"]');
                if (label.length === 0) {
                    label = $(radio).closest('label');
                }
                var label_radio = label.add(radio);
                label_radio.mousedown(function () {
                    $(radio).data('wasChecked', radio.checked);
                });
                label_radio.click(function () {
                    if ($(radio).data('wasChecked')) {
                        radio.checked = false;
                    }
                });
            });
        };
    })(jQuery);
    $('input[type=radio]').uncheckableRadio();

    $.ajaxSetup({
        beforeSend: function (xhr) {
            xhr.setRequestHeader('X-CSRFToken', document.getElementsByName('csrfmiddlewaretoken')[0].getAttribute('value'));
        },
    });

    function formToJSON(form) {
        return _.reduce(
            form.serializeArray(),
            (r, { name, value }) => {
                var swap = _.startsWith(name, 'data.');
                if (swap) {
                    var x = name.replace('data.', '');
                    name = value;
                    value = x;
                }
                if (_.has(r, name)) {
                    value = [..._.castArray(r[name]), value];
                }
                r[name] = swap ? _.castArray(value) : value;
                return r;
            },
            {}
        );
    }

    /*
- edit-req: req - questionnaire
- init-msg: mail - questionnaire
- edit-patient: patient - patient but has questionnaire
- intake-template: {{obj_key}}{{form_key}} - not necessarily
- TBD: plan
- TBD: follow-up
*/
    $('.save-btn').on('click', function (e) {
        e.preventDefault();
        var baseId = e.target.id;
        var form = $(`#${baseId}-form`);
        $(`#${baseId}`).prop('disabled', true);
        $(`#${baseId}-spinner`).show();
        var baseId = e.target.id;
        var baseNames = {
            'mail-deny': 'mail',
            'mail-send': 'mail',
            'mail-edit': 'mail',
            'rxt-stamp': 'rxt',
            'rq-new': 'rq',
            'rq-stamp': 'rq',
        };
        var baseName = _.get(baseNames, baseId, baseId);
        var form = $(`#${baseName}-form`);
        var url = form.attr('action');
        if (!url) {
            url = `/app/panel-html/{{model_str}}/{{o_pk}}/`;
        }
        var data = formToJSON(form);
        data['action'] = baseId;
        if (baseId == 'mail-deny') {
            //- validate doctor denial reasons
            var idr_ks = ['doc_idr', 'doc_idr2', 'doc_idr_details'];
            var idr_kvs = _.zipObject(
                idr_ks,
                idr_ks.map((k) => $(`#${k}`).val())
            );
            var err = false;
            // Remove existing error messages
            idr_ks.forEach((k) => {
                $(`#${k}`).parent().find('.text-danger').remove();
            });
            // Check for empty fields & add error messages
            _.keys(_.pickBy(idr_kvs, (v) => v === '')).forEach((k) => {
                err = true;
                var html = `<div class='text-danger'>This field is required</div>`;
                $(`#${k}`).parent().append(html);
            });
            if (!err && !idr_kvs.doc_idr2.includes(idr_kvs.doc_idr)) {
                err = true;
                var html = `<div class='text-danger'>Reason & Sub-Reason are inconsistent</div>`;
                $(`#doc_idr2`).parent().append(html);
            }
            if (err) {
                $(`#${baseId}`).prop('disabled', false);
                $(`#${baseId}-spinner`).hide();
                return;
            } else {
                //- Merge doc_idrs & formio data
                data = _.merge(data, idr_kvs);
            }
        }
        $.post({
            url: url,
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (data, textStatus, jqXHR) {
                $(`#${baseName}-success`).text(data);
                $(`#${baseName}-success`).show();
                location.reload();
            },
            error: function (jqXHR, textStatus, errorThrown) {
                var msg = `HTTP ${jqXHR.status}` + (jqXHR.responseText ? `: ${jqXHR.responseText}` : '');
                $(`#${baseName}-error`).text(msg);
                $(`#${baseName}-error`).show();
            },
            complete: function (jqXHR, textStatus) {
                $(`#${baseId}`).prop('disabled', false);
                $(`#${baseId}-spinner`).hide();
            },
        });
    });
</script>
<script>
    {% include "admin/panel/panel-formio.js" %}
</script>

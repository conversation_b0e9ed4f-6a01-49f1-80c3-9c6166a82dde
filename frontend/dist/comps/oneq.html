<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Intake - TeleTest.ca</title>
        <link href="/css/styles.css?v=3" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body>
        <div id="layoutDefault">
            <div id="layoutDefault_content">
                <main>
                    <div class="container">
                        <div class="card mt-3 formio-form">
                            <div class="card-header border-bottom"></div>
                            <div class="card-body">
                                <div class="row justify-content-center">
                                    <div class="col-xxl-6 col-xl-8">
                                        <!-- text-->
                                        <div class="d-flex flex-column gap-4">
                                            <div class="d-flex flex-column gap-1">
                                                <span class="fw-semibold text-secondary">Question 2</span>
                                                <h3 class="mb-0">The lifecycle methods are mainly used for ___.</h3>
                                            </div>
                                            <div class="d-flex flex-column gap-4">
                                                <!-- list group-->
                                                <div class="list-group">
                                                    <div class="list-group-item list-group-item-action" aria-current="true">
                                                        <!-- form check-->
                                                        <div class="form-check">
                                                            <input class="form-check-input" id="flexRadioDefault5" type="radio" name="flexRadioDefault" />
                                                            <label class="form-check-label stretched-link" for="flexRadioDefault5">keeping track of event history</label>
                                                        </div>
                                                    </div>
                                                    <!-- list group-->
                                                    <div class="list-group-item list-group-item-action">
                                                        <!-- form check-->
                                                        <div class="form-check">
                                                            <input class="form-check-input" id="flexRadioDefault7" type="radio" name="flexRadioDefault" />
                                                            <label class="form-check-label stretched-link" for="flexRadioDefault7">enhancing components</label>
                                                        </div>
                                                    </div>
                                                    <!-- list group-->
                                                    <div class="list-group-item list-group-item-action">
                                                        <!-- form check-->
                                                        <div class="form-check">
                                                            <input class="form-check-input" id="flexRadioDefault8" type="radio" name="flexRadioDefault" />
                                                            <label class="form-check-label stretched-link" for="flexRadioDefault8">freeing up resources</label>
                                                        </div>
                                                    </div>
                                                    <!-- list group-->
                                                    <div class="list-group-item list-group-item-action">
                                                        <!-- form check-->
                                                        <div class="form-check">
                                                            <input class="form-check-input" id="flexRadioDefault9" type="radio" name="flexRadioDefault" />
                                                            <label class="form-check-label stretched-link" for="flexRadioDefault9">none of the above</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- text-->
                                        <div class="d-flex flex-column gap-4">
                                            <div class="d-flex flex-column gap-1">
                                                <span class="fw-semibold text-secondary">Question 3</span>
                                                <h3 class="mb-3">___ can be done while multiple elements need to be returned from a component.</h3>
                                            </div>
                                            <div class="d-flex flex-column gap-4">
                                                <div>
                                                    <!-- buttons-->
                                                    <div class="d-grid mb-2" role="group" aria-label="Basic radio toggle button group">
                                                        <input class="btn-check" id="btnradio1" type="radio" name="btnradio" />
                                                        <label class="btn btn-outline-secondary text-start" for="btnradio1">Abstraction</label>
                                                    </div>
                                                    <!-- buttons-->
                                                    <div class="d-grid mb-2">
                                                        <input class="btn-check" id="btnradio2" type="radio" name="btnradio" />
                                                        <label class="btn btn-outline-secondary text-start" for="btnradio2">Packing</label>
                                                    </div>
                                                    <!-- buttons-->
                                                    <div class="d-grid mb-2">
                                                        <input class="btn-check" id="btnradio3" type="radio" name="btnradio" />
                                                        <label class="btn btn-outline-secondary text-start" for="btnradio3">Insulation</label>
                                                    </div>
                                                    <!-- buttons-->
                                                    <div class="d-grid mb-2">
                                                        <input class="btn-check" id="btnradio4" type="radio" name="btnradio" />
                                                        <label class="btn btn-outline-secondary text-start" for="btnradio4">Wrapping</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- text-->
                                        <div class="d-flex flex-column gap-4">
                                            <div class="d-flex flex-column gap-1">
                                                <span class="fw-semibold text-secondary">Question 3</span>
                                                <h3 class="mb-3">___ can be done while multiple elements need to be returned from a component.</h3>
                                            </div>
                                            <div class="d-flex flex-column gap-4">
                                                <div>
                                                    <div class="list-group">
                                                        <div class="list-group-item list-group-item-action" aria-current="true">
                                                            <div class="form-check">
                                                                <input class="form-check-input" id="flexCheckboxDefault5" type="checkbox" name="flexCheckboxDefault" />
                                                                <label class="form-check-label stretched-link" for="flexCheckboxDefault5">301 is used for a temporary page redirection, 302 for a redirection block</label>
                                                            </div>
                                                        </div>
                                                        <!-- list group-->
                                                        <div class="list-group-item list-group-item-action">
                                                            <!-- form check-->
                                                            <div class="form-check">
                                                                <input class="form-check-input" id="flexCheckboxDefault6" type="checkbox" name="flexCheckboxDefault" />
                                                                <label class="form-check-label stretched-link" for="flexCheckboxDefault6">301 is used for a permanent page redirection, 302 for a temporary redirection</label>
                                                            </div>
                                                        </div>
                                                        <!-- list group-->
                                                        <div class="list-group-item list-group-item-action">
                                                            <!-- form check-->
                                                            <div class="form-check">
                                                                <input class="form-check-input" id="flexCheckboxDefault7" type="checkbox" name="flexCheckboxDefault" />
                                                                <label class="form-check-label stretched-link" for="flexCheckboxDefault7">301 is used for a page redirection block, 302 for a permanent redirection</label>
                                                            </div>
                                                        </div>
                                                        <!-- list group-->
                                                        <div class="list-group-item list-group-item-action">
                                                            <!-- form check-->
                                                            <div class="form-check">
                                                                <input class="form-check-input" id="flexCheckboxDefault8" type="checkbox" name="flexCheckboxDefault" />
                                                                <label class="form-check-label stretched-link" for="flexCheckboxDefault8">301 is used for a temporary page redirection, 302 for a permanent redirection</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Button-->
                                <div class="row justify-content-center">
                                    <div class="col-xxl-6 col-xl-8">
                                        <div class="d-flex justify-content-between">
                                            <button class="btn btn-secondary" onclick="courseForm.previous()">
                                                <i class="fe fe-arrow-left"></i>
                                                Previous
                                            </button>
                                            <button class="btn btn-primary" onclick="courseForm.next()">
                                                Next
                                                <i class="fe fe-arrow-right"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
            <div id="layoutDefault_footer"></div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script>
            var uuid = readFromStorage('uuid');
            $.ajax({
                type: 'POST',
                url: `/app/log-row/`,
                contentType: 'application/json',
                dataType: 'json',
                data: JSON.stringify({
                    uuid: uuid,
                    href: window.location.href,
                    HTTP_REFERER: document.referrer,
                }),
                success: function (data) {
                    if (data['uuid']) {
                        uuid = data['uuid'];
                        writeToStorage('uuid', uuid);
                    }
                    if (data['gclid']) {
                        writeToStorage('gclid', data['gclid']);
                    }
                },
            });
        </script>
        <script src="/js/scripts.js"></script>
    </body>
</html>
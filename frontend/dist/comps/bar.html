<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Intake - TeleTest.ca</title>
        <link href="/css/styles.css?v=3" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body>
<div class="card-body d-flex flex-column gap-5">
    <!-- quiz -->
    <div class="d-flex flex-md-row flex-column justify-content-between align-items-md-center gap-1 gap-md-0">
        <h3 class="mb-0"><a href="#" class="text-inherit">React State Management</a></h3>
        <div>
          <span class="text-danger">
            <i class="fe fe-clock me-1 align-middle"></i>
            00:05:55
          </span>
        </div>
      </div>
      <div class="d-flex flex-column gap-2">
        <!-- text -->
        <div class="d-flex justify-content-between">
          <span>Exam Progress:</span>
          <span>Question 1 out of 5</span>
        </div>
        <!-- progress bar -->
        <div class="">
          <div class="progress" style="height: 6px">
            <div class="progress-bar bg-success" role="progressbar" style="width: 15%" aria-valuenow="15" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
      <!-- text -->
      <div class="d-flex flex-column gap-4">
        <div class="d-flex flex-column gap-1">
          <span class="fw-semibold text-secondary">Question 1</span>
          <h3 class="mb-0">React is mainly used for building ___.</h3>
        </div>
        <div class="d-flex flex-column gap-4">
          <!-- list group -->
          <div class="list-group">
            <div class="list-group-item list-group-item-action" aria-current="true">
              <!-- form check -->
              <div class="form-check">
                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                <label class="form-check-label stretched-link" for="flexRadioDefault1">Database</label>
              </div>
            </div>
            <!-- list group -->
            <div class="list-group-item list-group-item-action">
              <!-- form check -->
              <div class="form-check">
                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2">
                <label class="form-check-label stretched-link" for="flexRadioDefault2">Connectivity</label>
              </div>
            </div>
            <!-- list group -->
            <div class="list-group-item list-group-item-action">
              <!-- form check -->
              <div class="form-check">
                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault3">
                <label class="form-check-label stretched-link" for="flexRadioDefault3">User interface</label>
              </div>
            </div>
            <!-- list group -->
            <div class="list-group-item list-group-item-action">
              <!-- form check -->
              <div class="form-check">
                <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault4">
                <label class="form-check-label stretched-link" for="flexRadioDefault4">Design Platform</label>
              </div>
            </div>
          </div>
          <!-- Button -->
          <div class="d-flex justify-content-end">
            <button class="btn btn-primary" onclick="courseForm.next()">
              Next
              <i class="fe fe-arrow-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script>
            var uuid = readFromStorage('uuid');
            $.ajax({
                type: 'POST',
                url: `/app/log-row/`,
                contentType: 'application/json',
                dataType: 'json',
                data: JSON.stringify({
                    uuid: uuid,
                    href: window.location.href,
                    HTTP_REFERER: document.referrer,
                }),
                success: function (data) {
                    if (data['uuid']) {
                        uuid = data['uuid'];
                        writeToStorage('uuid', uuid);
                    }
                    if (data['gclid']) {
                        writeToStorage('gclid', data['gclid']);
                    }
                },
            });
        </script>
        <script src="/js/scripts.js"></script>
    </body>
</html>
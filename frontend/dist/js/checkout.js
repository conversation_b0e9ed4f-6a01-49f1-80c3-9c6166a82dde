// ADRIANG-TODO:

// Add space after certain amount of characters for credit card number
// https://stackoverflow.com/a/70283835/726155
$('#cardNumber3').on('input', function () {
    $(this).val(function (index, value) {
        // Store cursor position
        let cursor = $(this).get(0).selectionStart;
        
        // Filter characters and shorten CC (expanded for later use)
        const filterSpace = value.replace(/\s+/g, '');
        const filtered = filterSpace.replace(/[^0-9]/g, '');
        const cardNum = filtered.substr(0, 16);
        // validate card number using 
        
        // Handle alternate segment length for American Express
        const partitions = cardNum.startsWith('34') || cardNum.startsWith('37') ? [4,6,5] : [4,4,4,4];
        
        // Loop through the validated partition, pushing each segment into cardNumUpdated
        const cardNumUpdated = [];
        let position = 0;
        partitions.forEach(expandCard => {
            const segment = cardNum.substr(position, expandCard);
            if (segment) cardNumUpdated.push(segment);
            position += expandCard;
        });
        
        // Combine segment array with spaces
        const cardNumFormatted = cardNumUpdated.join(' ');
        // Handle cursor position if user edits the number later
        if (cursor < cardNumFormatted.length - 1) {
            // Determine if the new value entered was valid, and set cursor progression
            cursor = filterSpace !== filtered ? cursor - 1 : cursor;
            setTimeout(() => {
                $(this).get(0).setSelectionRange(cursor, cursor, 'none');
            });
        }

        return cardNumFormatted;
  })
});

// add space for postal code
$('#postal').on('input', function () {
  $(this).val(function (index, value) {
    // Store cursor position
    let cursor = $(this).get(0).selectionStart;
    
    // Filter characters and shorten CC (expanded for later use)
    const filterSpace = value.replace(/\s+/g, '');
    const filtered = filterSpace.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    const cardNum = filtered.substr(0, 6);
    
    // Handle alternate segment length for American Express
    const partitions = [3,3];
    
    // Loop through the validated partition, pushing each segment into cardNumUpdated
    const cardNumUpdated = [];
    let position = 0;
    partitions.forEach(expandCard => {
      const segment = cardNum.substr(position, expandCard);
      if (segment) cardNumUpdated.push(segment);
      position += expandCard;
    });
    
    // Combine segment array with spaces
    const cardNumFormatted = cardNumUpdated.join(' ');
    // Handle cursor position if user edits the number later
    if (cursor < cardNumFormatted.length - 1) {
      // Determine if the new value entered was valid, and set cursor progression
        cursor = filterSpace !== filtered ? cursor - 1 : cursor;
      setTimeout(() => {
        $(this).get(0).setSelectionRange(cursor, cursor, 'none');
      });
    }
    
    return cardNumFormatted;
  })
});

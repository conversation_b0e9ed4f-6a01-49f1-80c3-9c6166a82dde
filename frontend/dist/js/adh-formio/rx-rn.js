var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Get a prescription</h1><p>The following questionnaire will:<ol><li>Allow you to choose your prescription medication</li><li>Determine your eligibility (certain medications are not eligible for online prescriptions or renewals)</li></ol>Note: insurance/OHIP is not required, however it can reduce out-of-pocket costs.</p>",
            "type": "content"
        },
        {
            "key": "topic",
            "type": "radio",
            "input": true,
            "label": "<strong>What are you interested in?</strong>",
            "inline": false,
            "values": [
    {"value": "search"               , "label": "<strong>Search</strong> for a specific prescription by name"},
    {"value": "cat_bcp"              , "label": "Birth control, period delay, or menopause"},
    {"value": "cat_derm"             , "label": "Dermatology, skin, warts, herpes, or Latisse"},
    {"value": "cat_men"              , "label": "Male hair loss or ED (e.g. viagra)"},
    {"value": "cat_wl"               , "label": "Weight loss or smoking cessation"},
    {"value": "cat_aa"               , "label": "Allergy, asthma, or EpiPen"},
    {"value": "cat_heart"            , "label": "Blood pressure, cholesterol, or warfarin"},
    {"value": "cat_met"              , "label": "Glucose monitors (CGM), diabetes, thyroid, GERD, IBS, or Gout"},
    {"value": "cat_preg"             , "label": "Diclectin, nipple ointment (APNO), or PregVit"},
    {"value": "other"                , "label": "Other" },
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
        },
        {
            "key": "rx_note",
            "html": "<small>Please note we do not offer prescriptions for the following:<ul><li>Mental health e.g. Lexapro, Sertraline/Zoloft</li><li>Controlled substances e.g. Adderall</li><li>Gabapentin or Pregabalin</li><li>SERMs e.g. tamoxifen</li></ul></small>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
        },
        {
            "key": "rx",
            "type": "select",
            "label": "Select your prescription (type to search):",
            "data": {
                "custom": "values = _.map(_.pickBy(data.rxd),(v,k)=>({value:k,label:_.get(v,`n`,k)}))"
            },
            "dataSrc": "custom",
            "multiple": false,
            "input": true,
            "tableView": true,
            "validate": { "required": true },
            "customConditional": "show = data.topic=='search'"
            // "defaultValue": {value:"alysena", label:"Alysena"},
        },
        {
            "key": "cat_bcp",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "fem_pn_ocp"                 , "label": "Birth control"},
    {"value": "fem_pn_suppress_menses"     , "label": "Delaying your period"},
    {"value": "fem_pn_ella"                , "label": "Emergency contraception aka Plan B (e.g. Ella)"},
    {"value": "vaginal_estrogen_medication", "label": "Medications for menopause (e.g. vaginal estrogen)"},
    {"value": "other"                      , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_bcp'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_derm",
            "type": "radio",
            "input": true,
            "label": "What condition are you seeking treatment for?",
            "values": [
    {"value": "std_pn_hsv"          , "label": "Herpes/HSV"},
    {"value": "wart_medication"     , "label": "Anogenital warts"},
    {"value": "rx_wrinkle"          , "label": "Wrinkles or fine lines e.g. Tretinoin"},
    {"value": "eyelash_medication"  , "label": "Eyelash growth e.g. Latisse®"},
    {"value": "derm_rx_balanitis"   , "label": "Balanitis"},
    {"value": "derm_rx_acne"        , "label": "Acne"},
    {"value": "derm_rx_rosacea"     , "label": "Rosacea"},
    {"value": "derm_rx_psoriasis"   , "label": "Psoriasis"},
    {"value": "derm_rx_eczema"      , "label": "Eczema"},
    {"value": "anti_fungal_medication", "label": "Nail fungus"},
    {"value": "derm_rx_jock"        , "label": "Jock itch"},
    {"value": "derm_rx_tinea_pedis" , "label": "Athlete’s foot"},
    {"value": "derm_con"            , "label": "Contact dermatitis"},
    {"value": "derm_seb"            , "label": "Seborrheic dermatitis"},
    {"value": "derm_rx_barbae"      , "label": "Razor bumps (folliculitis barbae)"},
    {"value": "cosmetic_derm_melasma"     , "label": "Melasma"},
    {"value": "derm_poison_ivy"     , "label": "Poison ivy or sumac exposure"},
    {"value": "stomatitis_medication", "label": "Canker sore (oral stomatitis)"},
    {"value": "other"               , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_derm'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_men",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "ed_medication"          , "label": "Erectile disfunction (ED) e.g. Viagra, Cialis"},
    {"value": "med_hair_loss"          , "label": "Hair loss e.g. Finasteride, Dutasteride"},
    {"value": "other"                  , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_men'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_wl",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "wl"                 , "label": "Weight loss e.g. Ozempic, Mounjaro"},
    {"value": "smoking_medication" , "label": "Smoking cessation"},
    {"value": "other"              , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_wl'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_aa",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "allergy_eye_drops" , "label": "Eye drops for allergies e.g. Pataday (Olopatadine)"},
    {"value": "nasal_steroids"    , "label": "Nasal sprays or steroids for allergies e.g. Flonase"},
    {"value": "allergy_medication", "label": "Other allergy medication"},
    {"value": "asthma_medication" , "label": "Asthma medication including inhalers & puffers"},
    {"value": "epipen_medication" , "label": "EpiPen"},
    {"value": "other"             , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_aa'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_heart",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "blood_pressure_medication", "label": "Blood pressure medication"},
    {"value": "cholesterol_medication"   , "label": "Cholesterol medication"},
    {"value": "inr_testing"              , "label": "Warfarin"},
    {"value": "other"                    , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_heart'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_met",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "cgm_prescription"   , "label": "Glucose monitors (CGMs)"},
    {"value": "diabetic_medication", "label": "Diabetes medication e.g. Metformin"},
    {"value": "rx_thyroid"         , "label": "Thyroid medication e.g. levothyroxine (Synthroid)"},
    {"value": "rx_gerd"            , "label": "Acid Reflux (GERD) medication"},
    {"value": "ibs_medication"     , "label": "Irritable Bowel Syndrome (IBS) medication"},
    {"value": "gout_medication"    , "label": "Gout medication"},
    {"value": "other"              , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_met'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_preg",
            "type": "radio",
            "input": true,
            "label": "What are you specifically interested in?",
            "values": [
    {"value": "fem_diclectin"             , "label": "Diclectin"},
    {"value": "fem_pregvit"               , "label": "PregVit"},
    {"value": "rx_newmans_nipple_ointment", "label": "Newman’s All Purpose Nipple Ointment (APNO)"},
    {"value": "other"                     , "label": "Other"},
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_preg'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "topic_other",
            "type": "textarea",
            "input": true,
            "label": "Please state what prescription(s) you’re interested in:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.topic=='other' || _.some(['bcp','derm','men','wl','aa','heart','met','preg'],c=>_.get(data,`cat_${c}`)=='other')"
        },
        {
            "key": "rxd",
            "type": "textfield",
            "input": true,
            "label": "Rx Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
epipen                     : {n: "EpiPen (Epinephrine autoinjector)",      skus: ["epipen_medication"]},
pataday                    : {n: "Pataday (Olopatadine) eye drops",        skus: ["allergy_eye_drops"]},
cromolyn                   : {n: "Cromolyn eye drops",                     skus: ["allergy_eye_drops"]},
blexten                    : {n: "Blexten (Bilastine)",                    skus: ["allergy_medication"]},
rupall                     : {n: "Rupall (Rupatadine fumarate)",           skus: ["allergy_medication"]},
advair                     : {n: "Advair (Salmeterol/Fluticasone)",        skus: ["asthma_medication"]},
flovent                    : {n: "Flovent (Fluticasone)",                  skus: ["asthma_medication"]},
symbicort                  : {n: "Symbicort (Budesonide/Formoterol)",      skus: ["asthma_medication"]},
salbutamol                 : {n: "Ventolin (Albuterol, Salbutamol)",       skus: ["asthma_medication"]},
pulmicort                  : {n: "Pulmicort (Budesonide, Entocort)",       skus: ["asthma_medication"]},
qvar                       : {n: "Qvar (Beclomethasone Dipropionate)",     skus: ["asthma_medication"]},
asmanex                    : {n: "Asmanex (Mometasone)",                   skus: ["asthma_medication"]},
breo                       : {n: "Breo Ellipta (Vilanterol/Fluticasone)",  skus: ["asthma_medication"]},
singulair                  : {n: "Singulair (Montelukast)",                skus: ["asthma_medication"]},
flonase                    : {n: "Flonase (Fluticasone)",                  skus: ["nasal_steroids"]},
nasonex                    : {n: "Nasonex (Mometasone)",                   skus: ["nasal_steroids"]},
rhinocort                  : {n: "Rhinocort (Budesonide)",                 skus: ["nasal_steroids"]},
dymista                    : {n: "Dymista (Azelastine/Fluticasone)",       skus: ["nasal_steroids"]},
omnaris                    : {n: "Omnaris (Ciclesonide nasal)",            skus: ["nasal_steroids"]},
warfarin                   : {n: "Warfarin (Coumadin)",                    skus: ["inr_testing"]},
amlodipine                 : {n: "Amlodipine (Norvasc)",                   skus: ["blood_pressure_medication"]},
atenolol                   : {n: "Atenolol (Tenormin)",                    skus: ["blood_pressure_medication"]},
bisoprolol                 : {n: "Bisoprolol (Zebeta)",                    skus: ["blood_pressure_medication"]},
chlorthalidone             : {n: "Chlorthalidone (Hygroton, Thalitone)",   skus: ["blood_pressure_medication"]},
diltiazem                  : {n: "Diltiazem (Cardizem)",                   skus: ["blood_pressure_medication"]},
enalapril                  : {n: "Enalapril (Vasotec)",                    skus: ["blood_pressure_medication"]},
hydrochlorothiazide        : {n: "Hydrochlorothiazide (Microzide)",        skus: ["blood_pressure_medication"]},
losartan                   : {n: "Losartan (Cozaar)",                      skus: ["blood_pressure_medication"]},
olmesartan                 : {n: "Olmesartan (Benicar)",                   skus: ["blood_pressure_medication"]},
lisinopril                 : {n: "Lisinopril (Qbrelis, Zestril, Prinivil)",skus: ["blood_pressure_medication"]},
perindopril                : {n: "Perindopril (Coversyl, Preterax)",       skus: ["blood_pressure_medication"]},
propranolol                : {n: "Propranolol (Inderal)",                  skus: ["blood_pressure_medication"]},
ramipril                   : {n: "Ramipril (Altace)",                      skus: ["blood_pressure_medication"]},
telmisartan                : {n: "Telmisartan (Micardis)",                 skus: ["blood_pressure_medication"]},
valsartan                  : {n: "Valsartan (Diovan)",                     skus: ["blood_pressure_medication"]},
atorvastatin               : {n: "Atorvastatin (Lipitor)",                 skus: ["cholesterol_medication"]},
rosuvastatin               : {n: "Rosuvastatin (Crestor)",                 skus: ["cholesterol_medication"]},
ezetimibe                  : {n: "Ezetimibe (Zetia)",                      skus: ["cholesterol_medication"]},
ectosone                   : {n: "Ectosone topical",                       skus: ["derm_rx_balanitis"]},
elocom                     : {n: "Elocom topical",                         skus: ["derm_rx_balanitis"]},
glycolic_acid              : {n: "Glycolic Acid",                          skus: ["derm_rx_balanitis"]},
mupirocin                  : {n: "Mupirocin (Bactroban)",                  skus: ["derm_rx_balanitis"]},
zoryve                     : {n: "Zoryve",                                 skus: ["derm_rx_balanitis"]},
adapalene                  : {n: "Adapalene (Differin)",                   skus: ["derm_rx_acne"]},
arazlo                     : {n: "Arazlo topical",                         skus: ["derm_rx_acne"]},
azelaic_acid_topical       : {n: "Azelaic Acid Topical (Finacea, Azelex)", skus: ["derm_rx_acne"]},
benzaclin                  : {n: "Benzaclin",                              skus: ["derm_rx_acne","derm_rx_barbae"]},
biacna                     : {n: "Biacna",                                 skus: ["derm_rx_acne"]},
doxycycline                : {n: "Doxycycline",                            skus: ["derm_rx_acne"]},
tactupump                  : {n: "Tactupump",                              skus: ["derm_rx_acne"]},
tazarotene                 : {n: "Tazarotene (Tazorac)",                   skus: ["derm_rx_acne"]},
tretinoin_topical          : {n: "Tretinoin Topical (Retin-A)",            skus: ["rx_wrinkle"]},
Lotrisone                  : {n: "Clotrimazole/Hydrocortisone (Lotrisone)",skus: ["derm_rx_jock"]},
fluconazole                : {n: "Fluconazole (Diflucan)",                 skus: ["derm_rx_jock"]},
ketoconazole_topical       : {n: "Ketoconazole Topical (Nizoral)",         skus: ["derm_rx_jock"]},
lamisil                    : {n: "Lamisil",                                skus: ["derm_rx_jock"]},
miconazole                 : {n: "Miconazole (Desenex)",                   skus: ["derm_rx_jock"]},
nystatin                   : {n: "Nystatin (Mycostatin)",                  skus: ["derm_rx_jock"]},
latisse                    : {n: "Latisse (Bimatoprost)",                  skus: ["eyelash_medication"]},
elidel                     : {n: "Elidel (pimecrolimus) topical",          skus: ["derm_rx_eczema"]},
eucrisa                    : {n: "Eucrisa (crisaborole) topical",          skus: ["derm_rx_eczema"]},
mometasone                 : {n: "Mometasone (Elocon)",                    skus: ["derm_rx_eczema"]},
pimecrolimus               : {n: "Pimecrolimus (Elidel)",                  skus: ["derm_rx_eczema"]},
protopic                   : {n: "Protopic (tacrolimus) topical",          skus: ["derm_rx_eczema"]},
tranexamic_acid_topical    : {n: "Tranexamic Acid Topical (Cyklokapron)",  skus: ["cosmetic_derm_melasma"]},
triple_combination_cream   : {n: "Triple Combination Cream (Tri-Luma)",    skus: ["cosmetic_derm_melasma"]},
jublia                     : {n: "Jublia (Efinaconazole)",                 skus: ["anti_fungal_medication"]},
enstilar                   : {n: "Enstilar",                               skus: ["derm_rx_psoriasis"]},
apprilon                   : {n: "Apprilon",                               skus: ["derm_rx_rosacea"]},
hydroquinone               : {n: "Hydroquinone",                           skus: ["derm_rx_acne"]},
kojic_acid                 : {n: "Kojic Acid",                             skus: ["derm_rx_acne"]},
clobex                     : {n: "Clobex",                                 skus: ["derm_rx_psoriasis"]},
oracort                    : {n: "Oracort",                                skus: ["derm_rx_psoriasis"]},
dapagliflozin              : {n: "Dapagliflozin (Farxiga)",                skus: ["diabetic_medication"]},
humalog                    : {n: "Insulin lispro (Humalog)",               skus: ["diabetic_medication"]},
janumet                    : {n: "Janumet (Sitagliptin/Metformin)",        skus: ["diabetic_medication"]},
lantus                     : {n: "Lantus (Insulin glargine)",              skus: ["diabetic_medication"]},
metformin                  : {n: "Metformin (Glucophage)",                 skus: ["diabetic_medication"]},
novorapid                  : {n: "Novorapid",                              skus: ["diabetic_medication"]},
pantoprazole               : {n: "Pantoprazole (Protonix)",                skus: ["rx_gerd"]},
allopurinol                : {n: "Allopurinol (Zyloprim)",                 skus: ["gout_medication"]},
colchicine                 : {n: "Colchicine (Colcrys)",                   skus: ["gout_medication"]},
prednisone                 : {n: "Prednisone (Deltasone)",                 skus: ["gout_medication"]},
sildenafil                 : {n: "Sildenafil (Viagra, Revatio)",           skus: ["ed_medication"]},
tadalafil                  : {n: "Tadalafil (Cialis)",                     skus: ["ed_medication"]},
vardenafil                 : {n: "Vardenafil (Levitra)",                   skus: ["ed_medication"]},
dutasteride                : {n: "Dutasteride (Avodart)",                  skus: ["med_hair_loss"]},
finasteride                : {n: "Finasteride (Propecia, Proscar)",        skus: ["med_hair_loss"]},
lyderm                     : {n: "Lyderm (Lidoderm)",                      skus: ["derm_rx_psoriasis"]},
varenicline                : {n: "Varenicline (Champix)",                  skus: ["smoking_medication"]},
zyban                      : {n: "Zyban (Bupropion)",                      skus: ["smoking_medication"]},
acyclovir                  : {n: "Acyclovir (Zovirax)",                    skus: ["std_pn_hsv"]},
famciclovir                : {n: "Famciclovir (Famvir)",                   skus: ["std_pn_hsv"]},
valacyclovir               : {n: "Valacyclovir (Valtrex)",                 skus: ["std_pn_hsv"]},
podofilox                  : {n: "Podofilox (Condylox)",                   skus: ["wart_medication"]},
sinecatechins_topical      : {n: "Sinecatechins Topical (Veregen)",        skus: ["wart_medication"]},
betamethasone              : {n: "Betamethasone (Celestone, Diprosone)",   skus: ["derm_rx_psoriasis"]},
vitamin_d                  : {n: "Vitamin D",                              skus: ["vit_d_pnl"]},
levothyroxine              : {n: "Levothyroxine (Synthroid, Levoxyl)",     skus: ["rx_thyroid"]},
liraglutide                : {n: "Liraglutide (Victoza, Saxenda)",         skus: ["wl"]},
semaglutide                : {n: "Semaglutide (Ozempic, Wegovy)",          skus: ["wl"]},
tirzepatide                : {n: "Tirzepatide (Mounjaro)",                 skus: ["wl"]},
mpa                        : {n: "MPA (Medroxyprogesterone Acetate)",      skus: ["fem_pn_suppress_menses"]},
neta                       : {n: "NETA (Norethindrone Acetate)",           skus: ["fem_pn_suppress_menses"]},
alesse                     : {n: "Alesse",                                 skus: ["fem_pn_ocp"]},
alysena                    : {n: "Alysena",                                skus: ["fem_pn_ocp"]},
constella                  : {n: "Constella",                              skus: ["fem_pn_ocp"]},
depo_provera               : {n: "Depo-Provera",                           skus: ["fem_pn_ocp"]},
diane_35                   : {n: "Diane 35",                               skus: ["fem_pn_ocp"]},
ella                       : {n: "Ella",                                   skus: ["fem_pn_ocp"]},
evra                       : {n: "Evra",                                   skus: ["fem_pn_ocp"]},
freya                      : {n: "Freya",                                  skus: ["fem_pn_ocp"]},
indayo                     : {n: "Indayo",                                 skus: ["fem_pn_ocp"]},
linessa                    : {n: "Linessa",                                skus: ["fem_pn_ocp"]},
lolo                       : {n: "Lolo",                                   skus: ["fem_pn_ocp"]},
estradiol                  : {n: "Norgestimate/Ethinyl Estradiol",         skus: ["fem_pn_ocp"]},
nuvaring                   : {n: "Nuvaring",                               skus: ["fem_pn_ocp"]},
ovima                      : {n: "Ovia",                                   skus: ["fem_pn_ocp"]},
portia                     : {n: "Portia",                                 skus: ["fem_pn_ocp"]},
tri_cira                   : {n: "Tri-Cira (Sprintec)",                    skus: ["fem_pn_ocp"]},
tri_cyclen                 : {n: "Tri-Cyclen",                             skus: ["fem_pn_ocp"]},
tri_jordyna                : {n: "Tri-Jordyna",                            skus: ["fem_pn_ocp"]},
triquilar                  : {n: "Triquilar",                              skus: ["fem_pn_ocp"]},
yasmin                     : {n: "Yasmin",                                 skus: ["fem_pn_ocp"]},
yaz                        : {n: "Yaz",                                    skus: ["fem_pn_ocp"]},
estrace                    : {n: "Estrace",                                skus: ["vaginal_estrogen_medication"]},
vagifem                    : {n: "Vagifem",                                skus: ["vaginal_estrogen_medication"]},
all_purpose_nipple_ointment: {n: "Newman All Purpose Nipple Oint. (APNO)", skus: ["rx_newmans_nipple_ointment"]},
diclectin                  : {n: "Diclectin",                              skus: ["fem_diclectin"]},
pregvit                    : {n: "Pregvit",                                skus: ["fem_pregvit"]},
iud                        : {n: "IUD",                                    skus: ["tbd_iud"]},
escitalopram               : {n: "Escitalopram (Lexapro, Cipralex)",       skus: ["tbd_mh"]},
sertraline                 : {n: "Sertraline (Zoloft)",                    skus: ["tbd_mh"]},
bupropion                  : {n: "Bupropion (Wellbutrin)",                 skus: ["tbd_mh"]},
trazodone                  : {n: "Trazodone",                              skus: ["tbd_mh"]},
vortioxetine               : {n: "Vortioxetine (Trintellix)",              skus: ["tbd_mh"]},
effexor                    : {n: "Effexor (Venlafaxine XR)",               skus: ["tbd_mh"]},
citalopram                 : {n: "Citalopram (Celexa)",                    skus: ["tbd_mh"]},
paroxetine                 : {n: "Paroxetine (Paxil)",                     skus: ["tbd_mh"]},
duloxetine                 : {n: "Duloxetine (Irenka, Cymbalta)",          skus: ["tbd_mh"]},
desvenlafaxine             : {n: "Desvenlafaxine (Pristiq)",               skus: ["tbd_mh"]},
aripiprazole               : {n: "Aripiprazole (Abilify)",                 skus: ["tbd_mh"]},
lithium                    : {n: "Lithium",                                skus: ["tbd_mh"]},
buspirone                  : {n: "Buspirone (Buspar)",                     skus: ["tbd_mh"]},
clonazepam                 : {n: "Clonazepam (Klonopin)",                  skus: ["tbd_cdsa"]},
clonidine                  : {n: "Clonidine (Catapres)",                   skus: ["tbd_mh"]},
zopiclone                  : {n: "Zopiclone (Imovane)",                    skus: ["tbd_mh"]},
lisdexamfetamine           : {n: "Lisdexamfetamine (Vyvanse)",             skus: ["tbd_cdsa"]},
amphetamine                : {n: "Adderall (Amphetamine)",                 skus: ["tbd_cdsa"]},
concerta                   : {n: "Methylphenidate (Concerta, Ritalin)",    skus: ["tbd_cdsa"]},
tamoxifen                  : {n: "Tamoxifen",                              skus: ["tbd_serm"]},
truvada                    : {n: "Truvada (PrEP)",                         skus: ["tbd_prep"]},
gabapentin                 : {n: "Gabapentin",                             skus: ["tbd_gaba"]},
pregabalin                 : {n: "Pregabalin (Lyrica)",                    skus: ["tbd_gaba"]},
oxycodone                  : {n: "Oxycodone",                              skus: ["tbd_cdsa"]}
            }
        },
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"ibs_medication"                : {"new": false, "n": "IBS medication"},
"stomatitis_medication"         : {"new":  true, "n": "canker sore medication"},
"fem_pn_ella"                   : {"new":  true, "n": "emergency contraception"},
"derm_rx_tinea_pedis"           : {"new":  true, "n": "athlete’s foot medication"},
"derm_con"                      : {"new":  true, "n": "contact dermatitis medication"},
"derm_seb"                      : {"new":  true, "n": "seborrheic dermatitis medication"},
"derm_poison_ivy"               : {"new":  true, "n": "poison ivy/sumac medication"},
"allergy_medication"            : {"new":  true, "n": "allergy medication"},
"nasal_steroids"                : {"new":  true, "n": "nasal steroids"},
"anti_fungal_medication"        : {"new":  true, "n": "anti-fungal medication"},
"asthma_medication"             : {"new":  true, "n": "asthma medication"},
"blood_pressure_medication"     : {"new": false, "n": "blood pressure medication"},
"cholesterol_medication"        : {"new":  true, "n": "cholesterol medication"},
"derm_rx_acne"                  : {"new":  true, "n": "acne medication"},
"derm_rx_balanitis"             : {"new":  true, "n": "balanitis medication"},
"derm_rx_barbae"                : {"new":  true, "n": "barbae medication"},
"derm_rx_eczema"                : {"new":  true, "n": "eczema medication"},
"derm_rx_jock"                  : {"new":  true, "n": "jock itch medication"},
"cosmetic_derm_melasma"               : {"new":  true, "n": "melasma medication"},
"derm_rx_psoriasis"             : {"new":  true, "n": "psoriasis medication"},
"derm_rx_rosacea"               : {"new":  true, "n": "rosacea medication"},
"diabetic_medication"           : {"new":  true, "n": "diabetic medication"},
"ed_medication"                 : {"new":  true, "n": "ED medication"},
"epipen_medication"             : {"new": false, "n": "EpiPen"},
"eyelash_medication"            : {"new":  true, "n": "Latisse"},
"fem_diclectin"                 : {"new":  true, "n": "diclectin"},
"fem_pn_ocp"                    : {"new":  true, "n": "birth control"},
"fem_pn_suppress_menses"        : {"new":  true, "n": "period delay"},
"fem_pregvit"                   : {"new":  true, "n": "PregVit"},
"gout_medication"               : {"new":  true, "n": "gout medication"},
"inr_testing"                   : {"new": false, "n": "Warfarin"},
"med_hair_loss"                 : {"new":  true, "n": "hair-loss medication"},
"rx_gerd"                       : {"new":  true, "n": "GERD medication"},
"rx_newmans_nipple_ointment"    : {"new":  true, "n": "APNO"},
"rx_thyroid"                    : {"new":  true, "n": "thyroid medication"},
"rx_wrinkle"                    : {"new":  true, "n": "skin cream"},
"smoking_medication"            : {"new":  true, "n": "smoking cessation medication"},
"std_pn_hsv"                    : {"new":  true, "n": "HSV medication"},
"vaginal_estrogen_medication"   : {"new": false, "n": "vaginal estrogen"},
"vit_d_pnl"                     : {"new":  true, "n": "vitamin D"},
"wart_medication"               : {"new":  true, "n": "wart medication"},
"weight_loss_medication_renewal": {"new": false, "n": "weight loss medication"},
"weight_loss_medication_start"  : {"new":  true, "n": "weight loss medication"},
"tbd_iud"                       : {"new":  true, "n": "IUD"},
"tbd_mh"                        : {"new":  true, "n": "mental health medication"},
"tbd_never"                     : {"new":  true, "n": "N/A"},
"tbd_prep"                      : {"new":  true, "n": "PrEP"},
            }
        },
        {
            "key": "final_product",
            "type": "textfield",
            "input": true,
            "label": "Final Product:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "p = _.get({weight_loss_medication_start: 49, weight_loss_medication_start: 29, cosmetic_derm_melasma: 109, inr_testing: 99}, data.sku0, 49); n=_.get(data.sd,data.sku0+'.n'); r=(data.new_rx=='yes'?'consultation':'renewal'); t=_.upperFirst(n)+' '+r; value = !data.sku0 ? null : {n:t, p:p, tp:'once', d: `Messaging consultation about ${n}.`}"
        },
        {
            "key": "sku0",
            "type": "textfield",
            "input": true,
            "label": "SKU0:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "v = data.topic=='search' ? _.get(data.rxd, `${data.rx.value}.skus.0`) : _.startsWith(data.topic,'cat_') ? _.get(data,data.topic) : data.topic; value = v=='other' ? null : v!='wl' ? v : data.new_rx=='yes' ? 'weight_loss_medication_start' : 'weight_loss_medication_renewal'"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Rx Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "v = !data.sku0 ? 'no_sku0' : _.some(['adh_','tbd_'], x=>_.startsWith(data.sku0, x)) ? data.sku0 : !data.new_rx ? 'ic' : data.new_rx=='yes'&&!_.get(data.sd, data.sku0+'.new') ? 'no_new_rx' : 0; ks = v ? 0 : _.compact(_.get(data, `ciks_${data.sku0}`, _.keys(_.pickBy(_.omit(_.get(data, `ci_${data.sku0}`), 'none'))))); value = v ? [v] : !_.isEmpty(ks) ? (_.some(['ci','ni','ic'], x=>_.includes(ks,x)) ? ks :_.concat('ci', ks)) : _.get(data, `ci_${data.sku0}.none`)===false ? ['ic'] : []"
        },
        {
            "key": "rx_idr_html",
            "html": "<div class='mt-3 text-red'><h3 class='text-red'>We’re unable to renew your prescription at this time.</h3><p>We are currently not able to renew prescriptions for {{data.sku0=='tbd_mh'?`mental health`:``}}{{data.sku0=='tbd_iud'?`IUDs`:``}}{{data.sku0=='tbd_cdsa'?`controlled substances`:``}}{{data.sku0=='tbd_serm'?`SERMs`:``}}{{data.sku0=='tbd_prep'?`PrEP`:``}}{{data.sku0=='tbd_gaba'?`Gabpentin or Pregabalin`:``}}.</p></div>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = data.rx && data.sku0.startsWith('tbd_')"
        },
        {
            "key": "new_rx",
            "type": "radio",
            "input": true,
            "label": "Are you interested in starting a new prescription or refilling/restarting/changing an existing prescription?",
            "inline": true,
            "values": [
                {
                    "label": "Starting a new prescription for the first time",
                    "value": "yes"
                },
                {
                    "label": "Renewal: refilling, restarting, or changing an existing prescription",
                    "value": "no"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = !!data.sku0",
            // "customConditional": "show = data.sku0 && ",
            "optionsLabelPosition": "right"
        },
        {
            "key": "rx_idr_html",
            "html": "<div class='mt-3 text-red'><h3 class='text-red'>We’re unable issue new prescriptions for {{_.get(data.sd,data.sku0+'.n')}}.</h3><p>Please select a different medication or select renewal if you have been previously issued a prescription.</p></div>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.idrs && _.includes(data.idrs, 'no_new_rx')"
        },
        {
            "key": "sex",
            "type": "radio",
            "input": true,
            "label": "What is your sex?",
            "inline": true,
            "values": [
                {
                    "label": "Female",
                    "value": "female"
                },
                {
                    "label": "Male",
                    "value": "male"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.new_rx && _.includes(['med_hair_loss'], data.sku0)",
            "optionsLabelPosition": "right"
        },
        {
            "key": "pregnant",
            "type": "radio",
            "input": true,
            "label": "Are you currently or possibly pregnant?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": false,
            "customConditional": "show = data.new_rx && _.includes(['rx_thyroid','wart_medication'], data.sku0)",
            "optionsLabelPosition": "right"
        },
        // allergy eye drops
        {
            "key": "ci_allergy_eye_drops",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Light sensitivity",
                    "value": "light_sensitivity"
                },
                {
                    "label": "Thick or pus-like discharge from your eye",
                    "value": "pus_discharge"
                },
                {
                    "label": "Blurry or hazy vision",
                    "value": "blurry_vision"
                },
                {
                    "label": "Loss of vision in one or both eyes",
                    "value": "vision_loss"
                },
                {
                    "label": "Eye pain",
                    "value": "eye_pain"
                },
                {
                    "label": "Concerned something is stuck in your eye",
                    "value": "object_in_eye"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show =  data.new_rx && data.sku0 == 'allergy_eye_drops'",
            "optionsLabelPosition": "right"
        },
        // asthma
        {
            "key": "ci_asthma_medication",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Difficulty speaking in sentences",
                    "value": "difficulty_speaking_in_sentences"
                },
                {
                    "label": "Feeling faint or lightheaded",
                    "value": "feeling_faint"
                },
                {
                    "label": "Chest pain",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of breath",
                    "value": "shortness_of_breath"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0=='asthma_medication'",
            "optionsLabelPosition": "right"
        },
        // blood pressure
        {
            "key": "ci_blood_pressure_medication",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Chest pain",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of breath",
                    "value": "sob"
                },
                {
                    "label": "New swelling in my legs, ankles, or feet",
                    "value": "edema"
                },
                {
                    "label": "Heart palpitations",
                    "value": "palpitations"
                },
                {
                    "label": "Headache or vision changes",
                    "value": "headache"
                },
                {
                    "label": "Numbness or weakness (face, arm, or leg)",
                    "value": "numbness_weakness"
                },
                {
                    "label": "Difficulty speaking or confusion",
                    "value": "speech_confusion"
                },
                {
                    "label": "Fainting or loss of consciousness",
                    "value": "syncope"
                },
                {
                    "label": "Nausea or vomiting",
                    "value": "nausea_vomiting"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "confirmLabel": "Symptoms present:",
            "customConditional": "show = data.new_rx && !_.includes(data.idrs, 'no_new_rx') && data.sku0=='blood_pressure_medication'",
            "optionsLabelPosition": "right"
        },
        // psoriasis
        {
            "key": "psoriasis_diagnosis",
            "type": "radio",
            "input": true,
            "label": "Have you been diagnosed with psoriasis by a doctor or nurse practitioner in the past?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "tooltip": "Your answer helps us understand your medical history and guide treatment options.",
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0=='derm_rx_psoriasis'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_derm_rx_psoriasis",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = data.psoriasis_diagnosis=='no' ? ['ni'] : []",
            "refreshOnChange": true
        },
        {
            "key": "hx_nitrates",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently taking any of the following nitrate medications?",
            "values": [
                {
                    "label": "Nitroglycerin (Nitro-Dur, Nitrostat, Trinitrin)",
                    "value": "nitroglycerin"
                },
                {
                    "label": "Isosorbide Mononitrate (Imdur, Monoket)",
                    "value": "isosorbide_mononitrate"
                },
                {
                    "label": "Isosorbide Dinitrate (Isordil, Sorbitrate)",
                    "value": "isosorbide_dinitrate"
                },
                {
                    "label": "Nitroprusside (Nitropress, Nipride)",
                    "value": "nitroprusside"
                },
                {
                    "label": "Amyl Nitrite (Poppers)",
                    "value": "amyl_nitrite"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0=='ed_medication'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cardiac_conditions",
            "type": "selectboxes",
            "input": true,
            "label": "Do you have any of the following cardiac conditions?",
            "values": [
                {
                    "label": "Had a heart attack",
                    "value": "recent_heart_attack"
                },
                {
                    "label": "Diagnosed with Heart Failure (i.e. congestive heart failure)",
                    "value": "heart_failure"
                },
                {
                    "label": "Angina",
                    "value": "unstable_angina"
                },
                {
                    "label": "Have an arrhythmia (i.e. irregular heart beat)",
                    "value": "arrhythmia"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.hx_nitrates && data.hx_nitrates.none && !_.some(_.omit(data.hx_nitrates, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "hx_alpha_blockers",
            "type": "selectboxes",
            "input": true,
            "label": "Are you taking any of the following medications for high blood pressure or prostate problems?",
            "values": [
                {
                    "label": "Doxazosin (Cardura, Carduran, Cascor, Doxadura)",
                    "value": "doxazosin"
                },
                {
                    "label": "Prazosin (Minipress, Lysivane)",
                    "value": "prazosin"
                },
                {
                    "label": "Terazosin (Hytrin, Terapress)",
                    "value": "terazosin"
                },
                {
                    "label": "Tamsulosin (Flomax, Omnic, Pradif, Contiflo)",
                    "value": "tamsulosin"
                },
                {
                    "label": "Alfuzosin (Uroxatral, Xatger, Xatral)",
                    "value": "alfuzosin"
                },
                {
                    "label": "Silodosin (Rapaflo, Silodyx, Urorec)",
                    "value": "silodosin"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.cardiac_conditions && data.cardiac_conditions.none && !_.some(_.omit(data.cardiac_conditions, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "pde5_contra_symptoms",
            "type": "selectboxes",
            "input": true,
            "label": "Have you experienced any of the following?",
            "values": [
                {
                    "label": "A history of low blood pressure or severe hypotension",
                    "value": "severe_hypotension"
                },
                {
                    "label": "Severe or frequent headaches",
                    "value": "headaches"
                },
                {
                    "label": "Palpitations or chest pain",
                    "value": "palpitations"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "Dizziness or fainting spells",
                    "value": "dizziness"
                },
                {
                    "label": "Changes in vision (e.g., blurry vision or loss of vision)",
                    "value": "vision_changes"
                },
                {
                    "label": "Painful or prolonged erections",
                    "value": "priapism"
                },
                {
                    "label": "Chest tightness, especially if occurring during or after sexual activity",
                    "value": "chest_pain"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.hx_alpha_blockers && data.hx_alpha_blockers.none && !_.some(_.omit(data.hx_alpha_blockers, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_ed_medication",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.flatMap(['hx_nitrates', 'cardiac_conditions', 'hx_alpha_blockers', 'pde5_contra_symptoms'], k => _.some(_.omit(data[k], 'none')) ? [k] : [])"
        },
        // latisse / eyelash medication
        {
            "key": "ci_eyelash_medication",
            "type": "selectboxes",
            "input": true,
            "label": "Please select any of the following that apply to you:",
            "values": [
                {
                    "label": "Currently pregnant or breastfeeding",
                    "value": "pregnant"
                },
                {
                    "label": "A history of glaucoma, or are on glaucoma medication",
                    "value": "glaucoma"
                },
                {
                    "label": "A complete or recent sudden loss of eyelashes",
                    "value": "alopecia_eyelashes"
                },
                {
                    "label": "An allergic reaction to Latisse® before",
                    "value": "allergy"
                },
                {
                    "label": "Have ever been diagnosed with uveitis, iritis or a herpes infection of your eye",
                    "value": "uveitis_iritis_herpes"
                },
                {
                    "label": "Excessive worry about a specific part of your body, believing it to be flawed or imperfect, even though others may tell you they see no issue",
                    "value": "bmd_screen"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0=='eyelash_medication'",
            "optionsLabelPosition": "right"
        },
        // OCP
        {
            "key": "ci_fem_pn_ocp",
            "type": "selectboxes",
            "input": true,
            "label": "Please select all that apply:",
            "values": [
                {
                    "label": "Currently or possibly pregnant",
                    "value": "pregnant"
                },
                {
                    "label": "Had a baby within the last 42 days",
                    "value": "recent_baby"
                },
                {
                    "label": "High Blood Pressure",
                    "value": "dx_htn"
                },
                {
                    "label": "Deep Vein Thrombosis (DVT)",
                    "value": "dx_dvt"
                },
                {
                    "label": "Pulmonary Embolus (PE)",
                    "value": "dx_pe"
                },
                {
                    "label": "Breast Cancer (Past or Present)",
                    "value": "dx_breast_cancer"
                },
                {
                    "label": "I am 35+ and smoke tobacco products",
                    "value": "dx_smoker"
                },
                {
                    "label": "A history of migraine headaches with vision changes, muscle weakness, or numbness/tingling",
                    "value": "headache"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            // "defaultValue": {"pregnant":false, "baby_recently":false, "dx_htn": false, "dx_dvt":false, "dx_pe":false, "dx_breast_cancer":false, "dx_smoker": false, "migraine_aura": false, "none":true},
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.new_rx && _.includes(['fem_pn_ocp','fem_pn_suppress_menses'], data.sku0)",
            "optionsLabelPosition": "right"
        },
        // fem_pn_suppress_menses
        {
            "key": "ciks_fem_pn_suppress_menses",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.keys(_.pickBy(_.omit(data.ci_fem_pn_ocp, 'none')))"
        },
        // start hair loss rfs from vca-rx-finasteride
        {
            "key": "female_note",
            "html": "<p class='text-red'>We can only offer hair loss medication to males.</p><p>If you have set your sex to female by mistake please correct it.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.sex == 'female' && data.sku0 == 'med_hair_loss'"
        },
        {
            "key": "currently_taking_finasteride",
            "type": "radio",
            "input": true,
            "label": "Have you ever been on finasteride/dutasteride, or are you currently taking it?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0 == 'med_hair_loss' && data.sex == 'male';",
            "optionsLabelPosition": "right"
        },
        {
            "key": "sudden_hair_loss",
            "type": "radio",
            "input": true,
            "label": "Have you experienced a sudden loss of hair in the last 6 months?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sex=='male' && data.currently_taking_finasteride=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "renewal_note",
            "html": "<p class='text-red'>We offer renewals or new prescription starts for chronic hair loss changes (changes observed over a period longer than 6 months). Sudden onset hair loss can be associated with other medical conditions including alopecia aerata or telogen effluvium, which do not respond to finasteride or dutasteride.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='yes'"
        },
        {
            "key": "photo_new",
            "html": "<p class='text-green'>TeleTest physicians require a photo of your scalp (i.e. crown, temples, forehead) to initiate a new prescription of finasteride or dutasteride.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='no'"
        },
        {
            "key": "ciks_med_hair_loss",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.concat((data.sex == 'female' ? ['female'] : []), (data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='yes' ? ['no_new_rx_sudden_hl'] : []))",
        },
        // GERD
        {
            "key": "ci_rx_gerd",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following?",
            "values": [
                {
                    "label": "Chest pain, discomfort or tightness",
                    "value": "chest_pain"
                },
                {
                    "label": "Trouble breathing",
                    "value": "shortness_breath"
                },
                {
                    "label": "Difficulty swallowing",
                    "value": "difficulty_swallowing"
                },
                {
                    "label": "Throwing up blood",
                    "value": "vomiting_blood"
                },
                {
                    "label": "Dark or tarry stool",
                    "value": "dark_stool"
                },
                {
                    "label": "Nausea or vomiting",
                    "value": "persistent_vomiting"
                },
                {
                    "label": "Fatigue or weakness",
                    "value": "fatigue_weakness"
                },
                {
                    "label": "Abdominal pain",
                    "value": "abdominal_pain"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0 == 'rx_gerd'",
            "optionsLabelPosition": "right"
        },
        // start rx_thyroid vca
        {
            "key": "thyroid_in_person",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Fatigue",
                    "value": "fatigue"
                },
                {
                    "label": "Feel cold all the time",
                    "value": "cold_intolerance"
                },
                {
                    "label": "Constipated",
                    "value": "constipated"
                },
                {
                    "label": "Feel sad or depressed",
                    "value": "sad"
                },
                {
                    "label": "Have muscle aches/cramps",
                    "value": "muscle_aches_cramps"
                },
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Rapid, slow or irregular heart beat",
                    "value": "palpitations"
                },
                {
                    "label": "Shortness of breath or difficulty breathing",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel unwell",
                    "value": "malaise"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": { "required": true },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0=='rx_thyroid' && (data.sex=='male' || (data.pregnant=='no' && data.sex=='female'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "thyroid_med",
            "type": "radio",
            "input": true,
            "label": "Are you currently taking or have you ever taken thyroid medication?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = data.thyroid_in_person && data.thyroid_in_person.none && !_.some(_.omit(data.thyroid_in_person, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "hyperthyroidism_meds",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently taking or have you ever taken any of the following medications?",
            "values": [
                {
                    "label": "Methimazole",
                    "value": "methimazole"
                },
                {
                    "label": "Tapazole",
                    "value": "tapazole"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": { "required": true },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.thyroid_med=='yes'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_rx_thyroid",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "hyperthyroidism_meds",
            "calculateValue": "ks = _.concat(((data.thyroid_med=='no')?['no_new_rx']:[]),(data.pregnant=='yes'?['pregnant']:[]), (_.flatMap(['thyroid_in_person','hyperthyroidism_meds'], k => _.some(_.omit(data[k], 'none')) ? [k] : []))); value = !_.isEmpty(ks) ? ks : (!data.pregnant||!_.some(data.thyroid_in_person)||!data.thyroid_med) ? ['ic'] : []"
        },
        // vca-hsv-test
        {
            "key": "ci_std_pn_hsv",
            "type": "selectboxes",
            "input": true,
            "label": "Please select if any of the following apply to you:",
            "values": [
                {
                    "label": "Experiencing the 1st outbreak in your lifetime",
                    "value": "diagnosis_new_hsv"
                },
                {
                    "label": "Concern you may be experiencing a shingles outbreak",
                    "value": "shingles"
                },
                {
                    "label": "Concern you may be experiencing a herpes outbreak in your eye",
                    "value": "ocular_herpes"
                },
                {
                    "label": "A new rash or other symptoms that you're not sure is related to herpes",
                    "value": "diagnosis_uncertain_hsv"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": { "required": true },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0 == 'std_pn_hsv'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "hsv_immediate_note",
            "content": "<h3 class='text-red'>Please seek immediate attention at an urgent care clinic!</h3><p class='text-red'>{{data.ci_std_pn_hsv&&data.ci_std_pn_hsv.shingles&&data.ci_std_pn_hsv.ocular_herpes?`Treatment of an ocular herpes or shingles outbreak requires immediate assessment`:`Diagnosis of a herpes outbreak requires a time-sensitive PCR swab`}}.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = _.some(_.omit(data.ci_std_pn_hsv, 'none'))"
        },
        // vaginal estrogen
        {
            "key": "ci_vaginal_estrogen_medication",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Vaginal bleeding",
                    "value": "vaginal_bleeding"
                },
                {
                    "label": "Abdominal pain",
                    "value": "abdominal_pain"
                },
                {
                    "label": "New skin rash",
                    "value": "new_skin_rash"
                },
                {
                    "label": "Spotting between cycles",
                    "value": "spotting_between_cycles"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.new_rx && data.sku0 == 'vaginal_estrogen_medication'",
            "optionsLabelPosition": "right"
        },
        // vitamin D
        {
            "key": "ci_vit_d_pnl",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Abdominal Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Constipation",
                    "value": "constipation"
                },
                {
                    "label": "Vomiting",
                    "value": "vomiting"
                },
                {
                    "label": "Frequent urination above my baseline/normal",
                    "value": "polyuria"
                },
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain_pressure"
                },
                {
                    "label": "Shortness of breath or difficulty breathing",
                    "value": "dyspnea"
                },
                {
                    "label": "Lightheaded/feeling faint",
                    "value": "presyncope"
                },
                {
                    "label": "Excessive thirst",
                    "value": "excessive_thirst"
                },
                {
                    "label": "Rapid, slow or irregular heart beat",
                    "value": "palpitations"
                },
                {
                    "label": "Feel unwell",
                    "value": "malaise"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.new_rx && data.sku0 == 'vit_d_pnl'",
            "optionsLabelPosition": "right"
        },
        // vca-rx-warts
        {
            "key": "wart_dx",
            "type": "radio",
            "input": true,
            "label": "Have you been diagnosed with Genital Warts or Molloscum Contagiosum by a healthcare provider (i.e. doctor, nurse practitioner)?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": false,
            "customConditional": "show = data.new_rx && data.sku0=='wart_medication' && data.pregnant=='no'"
        },
        {
            "key": "wart_dx_uncertain",
            "type": "radio",
            "input": true,
            "label": "Do you have a new undiagnosed rash that you're unsure is related to Genital Warts or Molloscum Contagiosum?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": false,
            "customConditional": "show = data.sku0=='wart_medication' && data.wart_dx=='yes'"
        },
        {
            "key": "wart_treatment_methods",
            "type": "radio",
            "input": true,
            "label": "<p>TeleTest physicians do not offer prescriptions for Imiquimod Cream, or treatment via liquid nitrogen, laser, or surgery.</p>Are you interested in treatment via gel (Podofilox) or ointment (Sinecatechins)?",
            "inline": false,
            "confirm_label": "Are you interested in treatment via gel (Podofilox) or ointment (Sinecatechins)?",
            "errorLabel": "Are you interested in treatment via gel (Podofilox) or ointment (Sinecatechins)?",
            "values": [
                {
                    "label": "Yes (required for TeleTest treatment)",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "clearOnHide": false,
            "customConditional": "show = data.wart_dx=='yes' && data.wart_dx_uncertain=='no'"
        },
        {
            "key": "ciks_wart_medication",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "ks = data.pregnant=='yes' ? ['pregnant'] : data.wart_dx=='no' ? ['ni'] : data.wart_dx_uncertain=='yes' ? ['wart_dx_uncertain'] : data.wart_treatment_methods=='no'?['wart_treatment'] : []; value = !_.isEmpty(ks) ? ks : _.some(['sex','pregnant','wart_dx','wart_dx_uncertain','wart_treatment_methods'],x=>!_.get(data,x)) ? ['ic'] : []"
        },
    ]
},
{
    "title": "Preferences",
    "type": "panel",
    "key": "page1",
    "components": [
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from prescription renewal, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                  "label": "Hair regrowth",
                  "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Prescription renewal for something else",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "validate": {"required": true},
            // "defaultValue": "none",
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "wizard-blurb",
            "type": "wizard-blurb",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "data",
            "customConditional": "show = !!data.showSubmit && !!data.final_product"
        },
        {
            "key": "faq_title",
            "html": "<h4>FAQs</h4><hr>",
            "type": "content",
            "customConditional": "show = !!data.showSubmit",
        },
        {
            "key": "faq",
            "type": "wizard-faq",
            "customConditional": "show = !!data.showSubmit",
            "rows": [
                {
                    "q": "How can I reimburse with my private insurance or Health Spending Account (HSA)?",
                    "p": "<p>Most insurance plans cover online/virtual medical consultation with a physician. We will send you a receipt for your consultation or subscription, which you can use to seek reimbursement from your private insurance or HSA.</p><p>If you require specific details just email <NAME_EMAIL> for a detailed receipt suitable for your benefits claims.</p>"
                },
                {
                    "q": "Are video visits required?",
                    "p": "<p><strong>No video visit is required;</strong> a provider will review your information 100% online. If prescribed, you’ll get online access to message your provider.</p>"
                },
                {
                    "q": "Will my current doctor find out about TeleTest? Will this affect roster status?",
                    "p": "<p><strong>No.</strong> TeleTest appointments are not OHIP-funded &amp; will not affect roster status.</p>"
                }
            ]
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

var cfcs = [
  {
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
          "key": "start_note",
          "html": "<h1>Check your testosterone</h1><p>The following questionnaire will:<ol><li>Provide a recommendation based on 3-5 quick questions</li><li>Determine insurance/OHIP eligibility</li><li>Determine total cost</li></ol>Note: insurance/OHIP is not required, however it can reduce out-of-pocket costs.</p>",
          "type": "content"
        },
        {
            "key": "ci_ped_trt",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Chest pain or heaviness",
                    "value": "chest_pain"
                },
                {
                    "label": "Abdominal pain or cramping",
                    "value": "abdominal_pain"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel lightheaded or faint",
                    "value": "presyncope"
                },
                {
                    "label": "Arm or leg swelling",
                    "value": "limb_swelling"
                },
                {
                    "label": "Heart palpitations (slow or fast heart beat)",
                    "value": "palpitations"
                },
                {
                    "label": "Coughing up blood",
                    "value": "hemoptysis"
                },
                {
                    "label": "Muscle weakness or cramping",
                    "value": "muscle_cramps"
                },
                {
                    "label": "Erections lasting more than 2 hours",
                    "value": "priapism"
                },
                {
                    "label": "Testicular lumps or pain",
                    "value": "testicular_mass"
                },
                {
                    "label": "Thoughts of self-harm, suicide or of hurting others",
                    "value": "si_hi_screen"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "optionsLabelPosition": "right"
        },
        {
            "key": "testing_goal",
            "type": "selectboxes",
            "input": true,
            "label": "What are your goals with lab testing/blood work? (Please check all that apply)",
            "inline": false,
            "clearOnHide": false,
            "confirm_label": "What are your goals with lab testing/blood work?",
            "errorLabel": "What are your goals with lab testing/blood work?",
            "values": [
                {
                    "label": "I want to adjust my dose(s) on my current drug regimen",
                    "value": "adjust_dose"
                },
                {
                  "label": "I am interested in minimizing harmful effects of PEDs or TRT",
                  "value": "harm_reduction"
                },
                {
                    "label": "I want to start testosterone replacement therapy (TRT)",
                    "value": "starting_trt"
                },
                {
                    "label": "I want to start taking PEDs other than testosterone",
                    "value": "starting_ped"
                },
                {
                    "label": "I want to check if I have low testosterone",
                    "value": "low_testosterone"
                },
                {
                  "label": "I am just curious",
                  "value": "curious"
                },
                {
                    "label": "I have a specific reason",
                    "value": "specific"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none'))"
        },
        {
            "key": "testing_goal_specific",
            "type": "textarea",
            "input": true,
            "label": "Please state your reason for pursuing lab testing/blood work:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.testing_goal.specific"
        },
        {
            "key": "fu_goal",
            "type": "selectboxes",
            "input": true,
            "label": "<p>Sometimes, test results may indicate areas where follow-up is needed.</p>Depending on the results, this may be:<ul><li>Prescription medication</li><li>Specialist referrals</li><li>Guidance or lifestyle advice</li></ul>What best describes your desired follow-up? (Please check all that apply)",
            "clearOnHide": false,
            "confirm_label": "Desired Follow-Up",
            "errorLabel": "Desired Follow-Up",
            "values": [
                {
                    "label": "Only lab testing results",
                    "value": "only_results"
                },
                {
                    "label": "Prescription medication (if appropriate, e.g. cholesterol lowering drugs)",
                    "value": "rx"
                },
                {
                    "label": "Specialist referrals (if appropriate, e.g. referral to cardiologist)",
                    "value": "referral"
                },
                {
                    "label": "Guidance on lifestyle changes",
                    "value": "guidance_lifestyle"
                },
                {
                    "label": "Guidance on result interpretation",
                    "value": "guidance_interpretation"
                },
                {
                    "label": "I have a specific request",
                    "value": "specific"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && _.some(data.testing_goal)"
        },
        {
            "key": "fu_goal_specific",
            "type": "textarea",
            "input": true,
            "label": "Please state your specific request:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.fu_goal.specific"
        },
        {
            "key": "ic",
            "type": "textfield",
            "input": true,
            "label": "Incomplete Key:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = !_.some(data.ci_ped_trt) ? 'ci_ped_trt' : !_.some(data.testing_goal) ? 'testing_goal' : !_.some(data.fu_goal) ? 'fu_goal' : !data.ped_kind ? 'ped_kind' : (data.ped_kind=='none'&&!data.low_t_symptoms) ? 'low_t_symptoms' : !data.desired_test_period ? 'desired_test_period' : !data.use_insurance ? 'use_insurance' : null"
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "ks = _.compact(_.keys(_.pickBy(_.omit(data.ci_ped_trt, 'none')))); value = !_.isEmpty(ks) ? (_.some(['ci','ni','ic'], x=>_.includes(ks,x)) ? ks :_.concat('ci', ks)) : data.ic ? ['ic', data.ic] : []"
        },
    ]
  },
  {
    "title": "Tests",
    "type": "panel",
    "key": "page3",
    "components": [
        {
            "key": "ped_kind",
            "type": "radio",
            "input": true,
            "label": "What best describes what you are currently taking or have taken in the past?",
            "confirm_label": "What you are currently taking/have taken in the past?",
            "errorLabel": "What you are currently taking/have taken in the past?",
            "inline": false,
            "values": [
                {
                    "label": "Doctor-prescribed testosterone (TRT) but no anabolic steroids or other PEDs",
                    "value": "trt"
                },
                {
                  "label": "Anabolic steroids or other performance enhancing drugs (PEDs) such as SARMs",
                  "value": "ped"
                },
                {
                  "label": "No testosterone or anabolic steroids",
                  "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && _.some(data.fu_goal)"
        },
        {
            "key": "low_t_symptoms",
            "type": "radio",
            "input": true,
            "label": "Low testosterone may cause:<ul><li>Low energy or fatigue</li><li>Reduced muscle mass or strength</li><li>Reduced sexual desire/lower libido</li><li>Difficulty achieving or maintaining erections</li><li>Mood changes or irritability</li><li>Increased body fat or difficulty losing weight</li></ul>Are you currently experiencing any of these symptoms?",
            "confirm_label": "Are you currently experiencing any low testosterone symptoms?",
            "errorLabel": "Are you currently experiencing any low testosterone symptoms?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.ped_kind == 'none'"
        },
        {
          "key": "recommended_area",
          "type": "textfield",
          "input": true,
          "label": "Recommended Area:",
          "hidden": true,
          "disabled": true,
          "multiple": false,
          "tableView": true,
          "clearOnHide": false,
          "calculateValue": "value = data.ped_kind=='trt' ? 'trt' : data.ped_kind=='ped' ? 'ped' : data.ped_kind!='none' ? null : data.low_t_symptoms=='yes' ? 'low_t' : 'lifestyle_t'",
          "refreshOnChange": true
      },
        {
            "key": "td",
            "type": "textfield",
            "input": true,
            "label": "Test Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
                "ALP"       : {"i":true, "p":  5, "n":"Alkaline Phosphatase (ALP)"},
                "ALT"       : {"i":true, "p":  5, "n":"Alanine Aminotransferase (ALT)"},
                "BILI"      : {"i":true, "p":  5, "n":"Bilirubin"},
                "CA"        : {"i":true, "p":  5, "n":"Calcium (Serum)"},
                "CBC"       : {"i":true, "p":  8, "n":"Complete Blood Count (CBC)"},
                "CN"        : {"i":true, "p":  3, "n":"Creatinine (eGFR)"},
                "CYSTATIN-C": {"i":true, "p": 50, "n":"Cystatin-C"},
                "E2"        : {"i":false,"p": 28, "n":"Estradiol"},
                "FBG"       : {"i":true, "p":  3, "n":"Blood Glucose"},
                "FREE-T"    : {"i":false,"p": 25, "n":"Free Testosterone"},
                "FSH"       : {"i":false,"p": 25, "n":"Follicle Stimulating Hormone (FSH)"},
                "HBA1C"     : {"i":true, "p": 11, "n":"HbA1c"},
                "K"         : {"i":true, "p":  3, "n":"Potassium"},
                "LH"        : {"i":false,"p": 25, "n":"Luteinizing Hormone (LH)"},
                "LP"        : {"i":true, "p": 20, "n":"Lipid Profile (LDL, HDL, Triglycerides)"},
                "PRL"       : {"i":false,"p": 28, "n":"Prolactin"},
                "PSA"       : {"i":false,"p": 30, "n":"Prostate Specific Antigen (PSA)"},
                "SHBG"      : {"i":false,"p": 42, "n":"Sex Hormone Binding Globulin"},
                "TT"        : {"i":true, "p": 20, "n":"Total Testosterone"},
                "SEMEN-FERTILITY":{"i":false, "p": 25, "n":"Semen/Sperm Fertility Analysis"},
            }
        },
        {
            "key": "pd",
            "type": "textfield",
            "input": true,
            "label": "Product Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
                "low_t"      :{"n":"Symptomatic Testosterone Testing", "d":"Messaging consultation to arrange bloodwork if you have symptoms of low testosterone.", "aks":['TT']},
                "trt"        :{"n":"TRT Testosterone Testing", "d":"Messaging consultation for bloodwork while on TRT.", "aks":['CBC','E2','FREE-T','SHBG','TT']},
                "lifestyle_t":{"n":"Lifestyle Testosterone Testing", "d":"Messaging consultation for bloodwork to track how lifestyle changes impact testosterone levels if you do not have symptoms.", "aks":['E2','FREE-T','SHBG','TT']},
                "ped"        :{"n":"PED Harm Reduction", "d":"Messaging consultation for bloodwork while on PEDs","aks":['ALP','ALT','CBC','CN','CYSTATIN-C','E2','FREE-T','HBA1C','K','LP','SHBG','TT']},
            }
        },
        {
            "key": "recommended_keys",
            "type": "textfield",
            "input": true,
            "label": "Recommended keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.get(data.pd, data.recommended_area+'.aks', [])",
            "refreshOnChange": true
        },
        {
            "key": "recommended_keys_html",
            "html": "Based on what you have told us above, our recommended tests are:<ul>{% data.recommended_keys.forEach(function(k, i) { %}<li>{{_.get(data.td, k+`.n`, k)}}</li>{% }) %}</ul>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "calculateValue": "value = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.ped_kind && (data.ped_kind!='none'||data.low_t_symptoms)",
            "customConditional": "show = data.recommended_keys_html"
        },
        {
            "key": "desired_test_period",
            "type": "radio",
            "input": true,
            "label": "How often are you interested in testing?",
            "values": [
                {
                    "label": "Not sure or based on doctor’s recommendation",
                    "value": "not_sure"
                },
                {
                    "label": "Monthly",
                    "value": "month"
                },
                {
                    "label": "Every 3 months",
                    "value": "3months"
                },
                {
                    "label": "Every 6 months",
                    "value": "6months"
                },
                {
                    "label": "Yearly",
                    "value": "year"
                },
                {
                    "label": "Once",
                    "value": "once"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.recommended_keys_html"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "let area2sku = {low_t:'mens_libido',trt:'mens_testosterone',lifestyle_t:'mens_testosterone_lifestyle',ped:'ped_screening_pnl'}; value = _.get(area2sku, data.recommended_area)",
            "refreshOnChange": true
        },
        {
            "key": "sub_period",
            "type": "textfield",
            "input": true,
            "label": "Subscription Period:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = !['month','3months','6months'].includes(data.desired_test_period) ? 'once' : data.desired_test_period",
            "refreshOnChange": true
        },
        {
            "key": "use_insurance",
            "type": "radio",
            "input": true,
            "label": "Do you have health insurance (e.g. OHIP) and wish to use it?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                  "label": "No",
                  "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.desired_test_period"
        }
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from lab testing, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "STI testing & treatment",
                    "value": "std"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                  "label": "Hair regrowth",
                  "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Mental health",
                  "value": "mental_health"
                },
                {
                  "label": "Prescription renewal",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_ped_trt.none') && !_.some(_.omit(data.ci_ped_trt, 'none')) && data.use_insurance"
        },
        {
            "key": "display_rejected_html",
            "html": "<h3 class='text-red'>You're ineligible for monitoring at this time. Please seek in-person care with a physician to discuss your concerns.</h3>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = !data.showSubmit"
        },
        {
            "key": "ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = (!data.recommended_keys||_.isEmpty(data.recommended_keys)) ? [] : _.filter(data.recommended_keys, k=>_.get(data.td,k+`.i`))",
            "refreshOnChange": true
        },
        {
            "key": "non_ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "Non-OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = (!data.recommended_keys||_.isEmpty(data.recommended_keys)||!data.ohip_keys) ? [] : _.filter(data.recommended_keys, k=>!data.ohip_keys.includes(k))",
            "refreshOnChange": true
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit"
        }
    ]
}
];

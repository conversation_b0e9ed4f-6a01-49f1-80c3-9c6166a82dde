var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
          "key": "start_note",
          "html": "<h1>Get lab testing</h1><p>The following questionnaire will:<ol><li>Determine your eligibility</li><li>Determine total cost</li></ol>Note: insurance/OHIP is not required, however it can reduce out-of-pocket costs.</p>",
          "type": "content"
        },
        {
            "key": "topic",
            "type": "radio",
            "input": true,
            "label": "<strong>What are you interested in?</strong>",
            "inline": false,
            "values": [
                {
                    "label": "STD/STI, BV, or UTI testing",
                    "value": "adh_std_ug"
                },
                {
                    "label": "Testosterone/PED testing for bodybuilding or TRT",
                    "value": "adh_ped_trt"
                },
                // {
                //     "label": "Testosterone/hormone testing for trans people",
                //     "value": "trans"
                // },
                {
                    "label": "Metabolic (e.g. cholesterol, diabetes, routine blood work)",
                    "value": "cat_metabolic"
                },
                {
                    "label": "Fertility",
                    "value": "cat_fertility"
                },
                {
                    "label": "Hair loss",
                    "value": "hair_loss_bloodwork"
                },
                {
                    "label": "Thyroid",
                    "value": "met_pn_thy"
                },
                {
                    "label": "Celiac",
                    "value": "celiac_pnl"
                },
                {
                    "label": "Immunity, vaccine titres, or tests for work/school/immigration",
                    "value": "cat_serology"
                },
                {
                    "label": "Anemia or iron deficiency",
                    "value": "fem_iron_deficiency"
                },
                {
                    "label": "Bone mineral density",
                    "value": "bmd_testing_women"
                },
                {
                    "label": "Cancer screening",
                    "value": "cat_cancer"
                },
                {
                    "label": "Pre-operative blood work or surgery-related lab tests",
                    "value": "pre_op_bloodwork"
                },
                {
                    "label": "Other",
                    "value": "other"
                },
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
        },
        {
            "key": "ped_trt_note",
            "html": "<h4 class='text-orange'>This questionnaire is not for testosterone monitoring</h4><p><a href='/app/adh/ped-trt/'>Click here to check your testosterone or if you are currently taking/interested in testosterone (TRT), anabolic steroids, or other PEDs.</a></p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.topic=='adh_ped_trt'"
        },
        {
            "key": "std_ug_note",
            "html": "<h4 class='text-orange'>This questionnaire is not for STD/STI, BV, or UTI testing</h4><p><a href='/app/adh/std-ug/'>Click here for STD/STI, BV, or UTI testing.</a></p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.topic=='adh_std_ug'"
        },
        {
            "key": "age",
            "type": "radio",
            "input": true,
            "label": "What is your age?",
            "values": [
                {
                    "label": "40 or older",
                    "value": "gte_40"
                },
                {
                    "label": "39 or younger",
                    "value": "lt_40"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_metabolic'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "last_test_years",
            "type": "radio",
            "input": true,
            "label": "When was your last test completed?",
            "values": [
                {
                    "label": "2 or more years ago",
                    "value": "gte_2"
                },
                {
                    "label": "Less than 2 years ago",
                    "value": "lt_2"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_metabolic' && data.age=='lt_40'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "metabolic_rf",
            "type": "selectboxes",
            "input": true,
            "label": "Do any of the following risk factors apply to you?",
            "values": [
                {
                    "label": "Previous tests had an abnormal value e.g. high cholesterol, diabetic, pre-diabetic",
                    "value": "abnormal_value"
                },
                {
                    "label": "Diagnosed with a medical condition that requires monitoring",
                    "value": "medical_condition"
                },
                {
                    "label": "Started on a medication that requires monitoring since last test",
                    "value": "rx_start"
                },
                {
                    "label": "Gained weight since last test",
                    "value": "weight_gain"
                },
                {
                    "label": "Started smoking or vaping since last test",
                    "value": "smoking_start"
                },
                {
                    "label": "Drink alcohol on a daily basis",
                    "value": "drinking"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                },
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_metabolic' && data.last_test_years=='lt_2'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_metabolic",
            "type": "textfield",
            "input": true,
            "label": "Metabolic SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "data",
            "calculateValue": "value = (data.age=='gte_40'||data.last_test_years=='gte_2'||_.some(_.omit(data.metabolic_rf,'none'))) ? 'met_pn_all' : 'longevity_bloodwork'"
        },
        {
            "key": "longevity_note",
            "html": "<p class='text-orange'>Please note that you may incur seperate charges at the lab as you do not have risk factors for metabolic screening.</p><p>The bloodwork listed on this panel isn't routinely recommended for those without risk factors, but is offered to individuals who feel monitoring these levels will guide choices for healthy living.</p><p>You may proceed if you feel that understanding these values will help you make better lifestyle choices.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.age=='lt_40'&&data.last_test_years=='lt_2'&&_.get(data, 'metabolic_rf.none')"
        },
    ]
},
{
    "title": "Eligibility",
    "type": "panel",
    "key": "page1",
    "components": [
        {
            "key": "cat_fertility",
            "type": "radio",
            "input": true,
            "label": "What is your sex?",
            "inline": true,
            "values": [
                {
                    "label": "Female",
                    "value": "fem_pn_amh"
                },
                {
                    "label": "Male",
                    "value": "mens_sperm_testing"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_fertility'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_serology",
            "type": "radio",
            "input": true,
            "label": "What test do you need?",
            "values": [
                {
                    "label": "Chest X-Ray",
                    "value": "chest_x_ray_panel"
                },
                {
                    "label": "Hepatitis A IgM",
                    "value": "hep_a_igm_pnl"
                },
                {
                    "label": "Tuberculosis (TB) IGRA Gold Test",
                    "value": "igra_test"
                },
                {
                    "label": "Isocyanate",
                    "value": "isocyanate_test"
                },
                {
                    "label": "Rabies titre",
                    "value": "rabies_panel"
                },
                {
                    "label": "Other testing for work, school, or immigration purposes",
                    "value": "immunity_pnl"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_serology'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cat_cancer",
            "type": "radio",
            "input": true,
            "label": "What test do you need?",
            "values": [
                {
                    "label": "Prostate Specific Antigen (PSA) test for prostate cancer screening",
                    "value": "mens_psa_testing"
                },
                {
                    "label": "Colon cancer screening: referral for a colonoscopy or Fecal Immunochemical Test (FIT)",
                    "value": "colon_cancer_screening"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            "validate": { "required": true },
            "tableView": false,
            "customConditional": "show = data.topic=='cat_cancer'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "topic_other",
            "type": "textarea",
            "input": true,
            "label": "Please state what test(s) you’re interested in:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.topic=='other' || (data.cat_cancer && data.cat_cancer=='other')"
        },
        {
            "key": "sku0",
            "type": "textfield",
            "input": true,
            "label": "SKU0:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "v = _.startsWith(data.topic,'cat_') ? _.get(data,data.topic) : data.topic; value = v=='other' ? null : v;"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "redrawOn": "data",
            "calculateValue": "v = !data.sku0 ? 'no_sku0' : _.some(['adh_','tbd_'], x=>_.startsWith(data.sku0, x)) ? data.sku0 : 0; ks = v ? 0 : _.compact(_.get(data, `ciks_${data.sku0}`, _.keys(_.pickBy(_.omit(_.get(data, `ci_${data.sku0}`), 'none'))))); value = v ? [v] : !_.isEmpty(ks) ? (_.some(['ci','ni','ic'], x=>_.includes(ks,x)) ? ks :_.concat('ci', ks)) : []"
        },
        // metabolic / longevity
        {
            "key": "ci_metabolic",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Dizziness, fainting spells, feel lightheaded or faint",
                    "value": "presyncope"
                },
                {
                    "label": "Chest pain or chest tightness (especially if occurring during or after activity)",
                    "value": "chest_pain"
                },
                {
                    "label": "Feel unwell or have a fever",
                    "value": "fever"
                },
                {
                    "label": "Heart palpitations (fast or irregular heartbeat)",
                    "value": "palpitations"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = _.includes(['met_pn_all','longevity_bloodwork'], data.sku0)",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ic_met",
            "type": "textfield",
            "input": true,
            "label": "Metabolic Incomplete Key:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = !_.includes(['met_pn_all','longevity_bloodwork'], data.sku0) ? 0 : !data.age ? 'age' : data.age=='gte_40' ? 0 : !data.last_test_years ? 'last_test_years' : data.last_test_years=='gte_2' ? 0 : !_.some(data.metabolic_rf) ? 'metabolic_rf' : 0;"
        },
        {
            "key": "ciks_met_pn_all",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "ci_metabolic",
            "calculateValue": "ks = _.keys(_.pickBy(_.omit(data.ci_metabolic, 'none'))); value = data.ic_met ? ['ic',`ic:${data.ic_met}`] : !_.isEmpty(ks) ? ks : !_.get(data, 'ci_metabolic.none') ? ['ic','ic:ci_metabolic'] : []"
        },
        // longevity-vca
        {
            "key": "ciks_longevity_bloodwork",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "ci_metabolic",
            "calculateValue": "ks = _.keys(_.pickBy(_.omit(data.ci_metabolic, 'none'))); value = data.ic_met ? ['ic',`ic:${data.ic_met}`] : !_.isEmpty(ks) ? ks : !_.get(data, 'ci_metabolic.none') ? ['ic','ic:ci_metabolic'] : []"
        },
        // semen-vca
        {
            "key": "had_vasectomy",
            "type": "radio",
            "input": true,
            "label": "Have you had a vasectomy?",
            "confirm_label": "Had a vasectomy:",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sku0=='mens_sperm_testing'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "vasectomy_timing",
            "type": "radio",
            "input": true,
            "label": "Has it been more than 3 months since your vasectomy?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.had_vasectomy=='yes'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "semen_analysis_reason",
            "type": "selectboxes",
            "input": true,
            "label": "Please select the reason(s) for requesting a semen analysis:",
            "values": [
                {
                    "label": "Trying to conceive with my partner without success",
                    "value": "trying_conceive"
                },
                {
                    "label": "I have a medical condition that may affect my fertility",
                    "value": "personal_medical_condition"
                },
                {
                    "label": "I take medication that could impact fertility",
                    "value": "medication_effect"
                },
                {
                    "label": "Previous abnormal semen analysis requiring follow-up",
                    "value": "previous_abnormal_test"
                },
                {
                    "label": "My partner has experienced a miscarriage",
                    "value": "partner_miscarriage"
                },
                {
                    "label": "My partner has a medical condition affecting fertility",
                    "value": "partner_medical_condition"
                },
                {
                    "label": "I need testing for sperm donation or surrogacy",
                    "value": "donation_surrogacy"
                },
                {
                    "label": "I have undergone surgery or treatment that may impact fertility",
                    "value": "surgery_treatment"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "selectboxes",
            "tableView": true,
            "customConditional": "show = data.had_vasectomy=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_mens_sperm_testing",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "had_vasectomy,vasectomy_timing,semen_analysis_reason",
            "calculateValue": "value = (data.had_vasectomy=='no' && _.get(data, 'semen_analysis_reason.none') && !_.some(_.omit(data.semen_analysis_reason, 'none'))) ? ['ni'] : (data.had_vasectomy=='yes' && data.vasectomy_timing=='no') ? ['vasectomy_timing'] : (!data.had_vasectomy||(!data.vasectomy_timing&&!_.some(data.semen_analysis_reason))) ? ['ic'] : []",
        },
        // vca-hair-loss
        {
            "key": "ci_hair_loss_bloodwork",
            "type": "selectboxes",
            "input": true,
            "label": "Have you started to experience any of the following symptoms around the time you started to lose your hair:",
            "values": [
                {
                    "label": "I am not experiencing hair loss",
                    "value": "ni"
                },
                {
                    "label": "Deeping of voice",
                    "value": "deep_voice"
                },
                {
                    "label": "Increase in muscle mass",
                    "value": "increase_in_muscle"
                },
                {
                    "label": "Women-only: increase/change in the size of my clitoris",
                    "value": "clitoromegaly"
                },
                {
                    "label": "Worsening of acne",
                    "value": "acne"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.sku0=='hair_loss_bloodwork'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_hair_loss_bloodwork",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "ci_hair_loss_bloodwork",
            "calculateValue": "ks = _.keys(_.pickBy(_.omit(data.ci_hair_loss_bloodwork, 'none'))); value = !_.isEmpty(ks) ? ks : !_.get(data, 'ci_hair_loss_bloodwork.none') ? ['ic','ic:ci_hair_loss_bloodwork'] : []"
        },
        // start: vca-rx-test-thyroid
        {
            "key": "ci_thyroid",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Heart palpitations (fast or irregular heartbeat)",
                    "value": "palpitations"
                },
                {
                    "label": "Fever (high temperature)",
                    "value": "fever"
                },
                {
                    "label": "Chest Pain or tightness",
                    "value": "chest_pain"
                },
                {
                    "label": "Unexpected weight loss (without trying)",
                    "value": "weight_loss"
                },
                {
                    "label": "Night sweats",
                    "value": "night_sweats"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "A new rash",
                    "value": "new_rash"
                },
                {
                    "label": "Nausea or vomiting",
                    "value": "nausea_vomiting"
                },
                {
                    "label": "Abdominal or pelvic pain/cramping",
                    "value": "abdo_pain"
                },
                {
                    "label": "Headaches (new or worsening)",
                    "value": "headache"
                },
                {
                    "label": "Feeling sad or depressed",
                    "value": "sad"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.sku0=='met_pn_thy'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "reason_thyroid_testing",
            "type": "selectboxes",
            "input": true,
            "label": "What is your reason for thyroid testing or treatment?",
            "values": [
                {
                    "label": "Family history or relative with hypothyroidism",
                    "value": "family_history"
                },
                {
                    "label": "I am currently taking thyroid medication or stopped thyroid medication",
                    "value": "on_medication"
                },
                {
                    "label": "I have symptoms of hypothyroidism (i.e. weight gain, fatigue)",
                    "value": "symptoms"
                },
                {
                    "label": "I am on a medication that affects thyroid function (e.g. lithium, amiodarone, stavudine)",
                    "value": "medication"
                },
                {
                    "label": "I have an autoimmune condition (e.g. celiac disease, type 1 diabetes, lupus, rheumatoid arthritis)",
                    "value": "autoimmune_comorbidity"
                },
                {
                    "label": "Had thyroid surgery",
                    "value": "thyroid_surgery"
                },
                {
                    "label": "Had previous neck/thyroid radiation",
                    "value": "irradiation"
                },
                {
                    "label": "Diagnosed with acromegaly, Cushing's disease, or a pituitary tumor",
                    "value": "pituitary_disease"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            "validate": { "required": true },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.ci_thyroid && data.ci_thyroid.none && !_.some(_.omit(data.ci_thyroid,'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "reason_thyroid_other",
            "type": "textarea",
            "input": true,
            "label": "Please state your reason for thyroid testing:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.other"
        },
        {
            "key": "thyroid_symptoms",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Fatigue or low energy",
                    "value": "fatigue"
                },
                {
                    "label": "Dry skin",
                    "value": "dry_skin",
                },
                {
                    "label": "Constipation",
                    "value": "constipation"
                },
                {
                    "label": "Feeling cold when others are comfortable",
                    "value": "cold_intolerance"
                },
                {
                    "label": "Weight gain without changes in diet or exercise",
                    "value": "weight_gain"
                },
                {
                    "label": "Thinning hair or hair loss",
                    "value": "hair_loss"
                },
                {
                    "label": "Heavy or irregular periods",
                    "value": "menstrual_irregularity"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": { "required": true },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.symptoms",
            "optionsLabelPosition": "right"
        },
        {
            "key": "thyroid_symptom_duration",
            "type": "radio",
            "input": true,
            "label": "How long have you had these symptoms?",
            "values": [
                {
                    "label": "Less than 12 weeks",
                    "value": "lt_12_weeks"
                },
                {
                    "label": "More than 12 weeks",
                    "value": "gt_12_weeks"
                }
            ],
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.symptoms && _.some(_.omit(data.thyroid_symptoms,'none'))"
        },
        {
            "key": "autoimmune_condition",
            "type": "select",
            "input": true,
            "label": "Which autoimmune condition do you have?",
            "data": {
                "values": [
                    {
                        "label": "Celiac disease",
                        "value": "celiac"
                    },
                    {
                        "label": "Type 1 diabetes",
                        "value": "type1_diabetes"
                    },
                    {
                        "label": "Lupus (SLE)",
                        "value": "lupus"
                    },
                    {
                        "label": "Rheumatoid arthritis",
                        "value": "rheumatoid_arthritis"
                    },
                    {
                        "label": "Psoriasis",
                        "value": "psoriasis"
                    },
                    {
                        "label": "Vitiligo",
                        "value": "vitiligo"
                    },
                    {
                        "label": "Multiple sclerosis",
                        "value": "ms"
                    },
                    {
                        "label": "Inflammatory bowel disease (Crohn's, ulcerative colitis)",
                        "value": "ibd"
                    },
                    {
                        "label": "Addison's disease",
                        "value": "addisons"
                    },
                    {
                        "label": "None of the above",
                        "value": "none"
                    }
                ]
            },
            "widget": "html5",
            "validate": {
                "required": true
            },
            "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.autoimmune_comorbidity === true"
        },
        {
            "key": "ic_thy",
            "type": "textfield",
            "input": true,
            "label": "Thyroid Incomplete Key:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = !_.includes(['met_pn_thy'], data.sku0) ? 0 : !_.some(data.ci_thyroid) ? 'ci_thyroid' : !_.some(data.reason_thyroid_testing) ? 'reason_thyroid_testing' : 0"
        },
        {
            "key": "ciks_met_pn_thy",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "other_thy_rfs",
            "calculateValue": "ks = _.some(_.omit(data.ci_thyroid,'none')) ? _.concat(_.keys(_.pickBy(_.omit(data.ci_thyroid, 'none'))),'ci_thyroid') : data.reason_thyroid_testing && data.reason_thyroid_testing.symptoms ? (data.thyroid_symptoms.none ? ['symptom_selected_but_none_chosen'] : data.thyroid_symptom_duration=='lt_12_weeks' ? _.concat('recent_symptoms',_.keys(_.pickBy(_.omit(data.thyroid_symptoms, 'none')))) : []) : []; value = !_.isEmpty(ks) ? ks : data.ic_thy ? ['ic',`ic:${data.ic_thy}`] : []"
        },
        // celiac_pnl: vca-test-celiac
        {
            "key": "celiac_diagnosis",
            "type": "radio",
            "input": true,
            "label": "Do you currently have a diagnosis of celiac disease?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sku0 == 'celiac_pnl'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "no_celiac_test_previous_celiac",
            "html": "<p class='text-red'>Celiac testing is not medically indicated if you have already been diagnosed with celiac disease.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.celiac_diagnosis == 'yes'"
        },
        {
            "key": "on_gluten_free_diet",
            "type": "radio",
            "input": true,
            "label": "Are you currently on a gluten free diet?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.celiac_diagnosis == 'no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "no_celiac_test_gluten_challenge",
            "html": "<p class='text-red'>Celiac testing requires a gluten challenge, or you can have a false negative result.</p><p>This consists of taking 8-10 grams (4-6 slices of bread per day) for 6-8 weeks before completing an celiac test.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.on_gluten_free_diet == 'yes'"
        },
        {
            "key": "celiac_rfs",
            "type": "selectboxes",
            "input": true,
            "label": "Do you have any of the following symptoms:",
            "values": [
                {
                    "label": "Fever",
                    "value": "fever"
                },
                {
                    "label": "Night Sweats",
                    "value": "night_sweats"
                },
                {
                    "label": "Weight Loss (Without Dietary/Activity Changes)",
                    "value": "weight_loss"
                },
                {
                    "label": "Rectal Bleeding (Red or Tarry/Black Stools)",
                    "value": "rectal_bleeding"
                },
                {
                    "label": "Feel lightheaded/faint",
                    "value": "preynscope"
                },
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of Breath",
                    "value": "dyspnea"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.celiac_diagnosis == 'no' && data.on_gluten_free_diet == 'no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "family_member_celiac",
            "type": "radio",
            "input": true,
            "label": "Do you have a 1st degree relative (mother, brother, father, sister) with a diagnosis of celiac disease (i.e. not gluten intolerance)?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.celiac_diagnosis == 'no' && data.on_gluten_free_diet == 'no' && data.celiac_rfs && data.celiac_rfs.none",
            "optionsLabelPosition": "right"
        },
        {
            "key": "celiac_symptoms",
            "type": "selectboxes",
            "input": true,
            "label": "Do you have any of the following?",
            "values": [
                {
                    "label": "Three or more loose bowel movements per day or chronic diarrhea",
                    "value": "chronic_diarrhea"
                },
                {
                    "label": "A history of iron deficency anemia",
                    "value": "iron_deficiency_anemia"
                },
                {
                    "label": "Greasy stools (Appear oily or float)",
                    "value": "steatorrhea"
                },
                {
                    "label": "Osteoperosis",
                    "value": "osteoperosis"
                },
                {
                    "label": "Bloating after eating",
                    "value": "bloating"
                },
                {
                    "label": "History of low B12 on bloodwork",
                    "value": "low_b12"
                },
                {
                    "label": "Eczema or rough skin over your elbows and front of your knees",
                    "value": "dermatitis_herpetiformis"
                },
                {
                    "label": "Diagnosed with hypothroidism",
                    "value": "hypothroidism_diagnosis"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.family_member_celiac=='no' && data.celiac_diagnosis=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "celiac_symptoms_greater_12_months",
            "type": "radio",
            "input": true,
            "label": "Has your symptoms/diagnosis been present for more than 12 months?",
            "inline": false,
            "values": [
                {
                    "label": "Yes; more than 12 months",
                    "value": "yes"
                },
                {
                    "label": "No; less than 12 months",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.some(_.omit(data.celiac_symptoms, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_celiac_pnl",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "ks = (data.family_member_celiac=='no' && data.celiac_symptoms && (data.celiac_symptoms.none || data.celiac_symptoms_greater_12_months=='no'))? ['ni'] : _.concat((_.some(_.omit(data.celiac_rfs, 'none')) ? ['celiac_rfs'] : []), (data.celiac_diagnosis=='yes' ? ['celiac_diagnosis'] : []), (data.on_gluten_free_diet=='yes' ? ['on_gluten_free_diet'] : [])); value = !_.isEmpty(ks) ? ks : (!data.celiac_diagnosis||!data.on_gluten_free_diet||!_.some(data.celiac_rfs)||!data.family_member_celiac||(data.family_member_celiac=='no'&&(!_.some(data.celiac_symptoms)||!data.celiac_symptoms_greater_12_months)))? ['ic'] : []"
        },
        // immunity_pnl - vca-employment
        {
            "key": "needle_stick",
            "type": "radio",
            "input": true,
            "label": "Are you concerned about an exposure through a needle stick injury, exposure through blood or blood products, or exposure to a hepatitis B or HIV positive person that occurred within the previous 7 days?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "defaultValue": false,
            "customConditional": "show = data.sku0 == 'immunity_pnl'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "needle_stick_note",
            "type": "htmlelement",
            "input": false,
            "label": "HTML",
            "content": "<h3 class='text-red'>Please seek immediate attention at an emergency clinic!</h3><p class='text-red'>Exposure to a needle stick or contaminated blood/blood products may require urgent <strong>post-exposure prophylaxis!</strong></p>",
            "tableView": false,
            "customConditional": "show = data.needle_stick=='yes'"
        },
        {
            "key": "recent_exposures",
            "type": "radio",
            "input": true,
            "label": "<p>Are you concerned about a recent exposure to any of the following?</p><ul><li>Measles</li><li>Mumps</li><li>Rubella</li><li>Varicella (chicken pox or shingles)</li><li>Hepatitis A, B, or C</li><li>HIV</li><li>Syphilis</li></ul>",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.needle_stick=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "exposure_note",
            "type": "htmlelement",
            "input": false,
            "label": "HTML",
            "content": "<h3 class='text-red'>TeleTest Isn't Appropriate For Recent Exposures</h3><p class='text-red'>We recommened seeking in-person care.</p>",
            "tableView": false,
            "customConditional": "show = data.needle_stick=='no' && data.recent_exposures=='yes'"
        },
        {
            "key": "ciks_immunity_pnl",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "ks = data.needle_stick=='yes' ? ['needle_stick'] : data.recent_exposures=='yes' ? ['exposure'] : []; value = !_.isEmpty(ks) ? ks : (!data.needle_stick||!data.recent_exposures) ? ['ic'] : []",
            "refreshOnChange": true
        },
        // h-pylori: test-pylori-vca
        {
            "key": "pylori_indication",
            "type": "selectboxes",
            "input": true,
            "label": "Please select if any of the following apply to you:",
            "values": [
                {
                    "label": "I have stomach discomfort or indigestion",
                    "value": "stomach_discomfort"
                },
                {
                    "label": "I've had a gastric or duodenal ulcer",
                    "value": "ulcers_history"
                },
                {
                    "label": "I have a family member with gastric (stomach) cancer",
                    "value": "gastric_cancer_history"
                },
                {
                    "label": "I have a family member with an H. Pylori infection",
                    "value": "family_pylori_infection"
                },
                {
                    "label": "I was born outside of Canada",
                    "value": "immigrant_high_prevalence"
                },
                {
                    "label": "None of these apply to me",
                    "value": "none"
                }
            ],
            "tooltip": "Check any that apply to you for better assessment.",
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sku0 == 'h_pylori'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "pylori_symptom_duration",
            "type": "radio",
            "input": true,
            "label": "Have your stomach discomfort or indigestion symptoms been present for:",
            "values": [
                {
                    "label": "Less than 6 months",
                    "value": "less_than_6_months"
                },
                {
                    "label": "More than 6 months",
                    "value": "more_than_6_months"
                }
            ],
            "tooltip": "Your answer helps determine the urgency and type of care needed.",
            "validate": {
                "required": false
            },
            "tableView": true,
            "conditional": {
                "json": {
                    "var": "data.pylori_indication.stomach_discomfort"
                }
            },
            "optionsLabelPosition": "right"
        },
        {
            "key": "pylori_contraindication",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Feel unwell",
                    "value": "malaise"
                },
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Palpitations (heart racing or fluttering)",
                    "value": "palpitations"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel lightheaded, dizzy, or have had fainting spells",
                    "value": "presyncope"
                },
                {
                    "label": "Rectal bleeding (i.e. blood in stool)",
                    "value": "rectal_bleeding"
                },
                {
                    "label": "Vomited blood or material that looks like coffee grounds",
                    "value": "vomiting_blood"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = _.some(_.omit(data.pylori_indication, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_h_pylori",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "_.concat((!_.some(_.omit(data.pylori_indication, 'none'))?['no_pylori_indication']:[]),(data.pylori_symptom_duration=='less_than_6_months'?['pylori_symptom_duration_less_than_6_months']:[]),(_.some(_.omit(data.pylori_contraindication, 'none'))?['has_pylori_contraindication']:[]))"
        },
        // vca-anemia
        {
            "key": "ci_fem_iron_deficiency",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Feel lightheaded or faint",
                    "value": "presyncope"
                },
                {
                    "label": "Palpitations (i.e. rapid, slow or irregular heart beat)",
                    "value": "palpitations"
                },
                {
                    "label": "Shortness of breath or difficulty breathing",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel unwell",
                    "value": "malaise"
                },
                {
                    "label": "Rectal bleeding (i.e. blood in stool)",
                    "value": "rectal_bleeding"
                },
                {
                    "label": "Recent changes in your period (i.e heavier flow, longer flow, or irregular cycles)",
                    "value": "menstrual_changes"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.sku0=='fem_iron_deficiency'",
            "optionsLabelPosition": "right"
        },
        // vca-psa-test
        {
            "key": "ci_mens_psa_testing",
            "type": "selectboxes",
            "input": true,
            "label": "Please select if any of the following apply to you:",
            "values": [
                {
                    "label": "Currently being treated or followed for prostate cancer",
                    "value": "prostate_cancer"
                },
                {
                    "label": "Fevers or Chills",
                    "value": "fever"
                },
                {
                    "label": "Bloody Urine",
                    "value": "hematuria"
                },
                {
                    "label": "Abdominal and/or Pelvic Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Unable to Urinate",
                    "value": "unable_to_urinate"
                },
                {
                    "label": "Penile Discharge",
                    "value": "penile_discharge"
                },
                {
                    "label": "Anal Discharge",
                    "value": "anal_discharge"
                },
                {
                    "label": "Feel Unwell",
                    "value": "malaise"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.sku0=='mens_psa_testing'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "psa_acknowledgement",
            "type": "checkbox",
            "input": true,
            "label": "<p>I understand that PSA testing is no longer recommended for routine screening for individuals without prostate symptoms. You're still entitled to screen and monitor your PSA levels. We recommend you review <a href=\"https://docs.teletest.ca/prostate-specific-antigen-psa\" target='_blank'>this content to give you a better understanding of the benefits and risks of PSA screening.</a></p>",
            "hidden": false,
            "validate": {
                "required": true
            },
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": false,
            "customConditional": "show = !_.some(_.omit(data.ci_mens_psa_testing, 'none')) && data.ci_mens_psa_testing && data.ci_mens_psa_testing.none"
        },
        // vca-tx-vitamin-d
        {
            "key": "ci_vit_d_pnl",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Abdominal Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Constipation",
                    "value": "constipation"
                },
                {
                    "label": "Vomiting",
                    "value": "vomiting"
                },
                {
                    "label": "Frequent urination above my baseline/normal",
                    "value": "polyuria"
                },
                {
                    "label": "Chest pain or pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of breath or difficulty breathing",
                    "value": "dyspnea"
                },
                {
                    "label": "Lightheaded/feeling faint",
                    "value": "presyncope"
                },
                {
                    "label": "Excessive thirst",
                    "value": "excessive_thirst"
                },
                {
                    "label": "Rapid, slow or irregular heart beat",
                    "value": "palpitations"
                },
                {
                    "label": "Feel unwell",
                    "value": "malaise"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.sku0 == 'vit_d_pnl'",
            "optionsLabelPosition": "right"
        },
        // vca-inr
        //
        {
            "key": "blood_pressure",
            "type": "radio",
            "input": true,
            "label": "We require a recent blood pressure measurement within the last 2 weeks. You can measure your blood pressure for free at a local pharmacy.<br><br><strong>Please confirm your blood pressure below:</strong>",
            "values": [
                {
                    "label": "Less than 140/90",
                    "value": "normotensive"
                },
                {
                    "label": "Greater than 140/90",
                    "value": "hypertensive"
                },
                {
                    "label": "I haven't checked my blood pressure",
                    "value": "bp_unknown"
                }
            ],
            "tableView": false,
            "customConditional": "show = data.sku0=='inr_testing'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ci_inr_testing",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Chest pain or heaviness",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel lightheaded or faint",
                    "value": "presyncope"
                },
                {
                    "label": "Arm or Leg Swelling",
                    "value": "limb_swelling"
                },
                {
                    "label": "Heart palpitations (fast or irregular heartbeat)",
                    "value": "palpitations"
                },
                {
                    "label": "Nausea or vomiting",
                    "value": "nausea_vomiting"
                },
                {
                    "label": "Coughing up blood",
                    "value": "hemoptysis"
                },
                {
                    "label": "New bruising",
                    "value": "bruising"
                },
                {
                    "label": "New bleeding (gums, eyes, colon)",
                    "value": "bleeding_new"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = data.sku0=='inr_testing' && data.blood_pressure=='normotensive'",
            "optionsLabelPosition": "right"
        },
        // vca-rabies
        {
            "key": "rabies_exposure",
            "type": "radio",
            "input": true,
            "label": "Are you concerned about an animal bite and/or rabies exposure?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": false,
            "customConditional": "show = data.sku0=='rabies_panel'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "rabies_exposure_note",
            "html": "<h3 class='text-red'>Your concern about an animal bite or potential rabies exposure requires immediate in-person medical assessment. Please seek urgent medical care for further evaluation.</h3>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.rabies_exposure=='yes'"
        },
        {
            "key": "ciks_rabies_panel",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "ks = (data.rabies_exposure=='yes') ? ['exposure'] : []; value = !_.isEmpty(ks) ? ks : !data.rabies_exposure ? ['ic'] : []",
        },
        // vca-test-lead
        {
            "key": "lead_indications",
            "type": "selectboxes",
            "input": true,
            "label": "Please select if any of the following apply to you:",
            "values": [
                {
                    "label": "Shooting range employee or instructor",
                    "value": "employee_shooting_range"
                },
                {
                    "label": "Soldier in the armed forces",
                    "value": "armed_forces"
                },
                {
                    "label": "Police officer",
                    "value": "police_officer"
                },
                {
                    "label": "Fire fighter",
                    "value": "fire_fighter"
                },
                {
                    "label": "Hunter",
                    "value": "hunter"
                },
                {
                    "label": "Amateur shooter with regular range visits",
                    "value": "amateur_shooter"
                },
                {
                    "label": "Professional athlete with regular range visits",
                    "value": "professional_shooter"
                },
                {
                    "label": "Have exposure to lead in the workplace",
                    "value": "workplace_exposure"
                },
                {
                    "label": "Have ongoing exposure to lead paint",
                    "value": "paint_exposure"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.sku0 == 'h_pylori'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "lead_indications_note",
            "html": "<h3 class='text-orange'>Lead testing is only indicated if you have exposure in one of the above.</h3>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = !_.some(_.omit(data.lead_indications, 'none')) && data.lead_indications && data.lead_indications.none"
        },
        {
            "key": "lead_contraindications",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Headaches",
                    "value": "headache"
                },
                {
                    "label": "Fatigue",
                    "value": "fatigue"
                },
                {
                    "label": "Abdominal or Pelvic Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Chest Pain or Pressure",
                    "value": "chest_pain"
                },
                {
                    "label": "Weight Loss",
                    "value": "weight_loss"
                },
                {
                    "label": "Joint or Muscle Pain",
                    "value": "joint_pain"
                },
                {
                    "label": "Feel sad or depressed",
                    "value": "sad"
                },
                {
                    "label": "Anxiety or Worried Thoughts",
                    "value": "anxiety"
                },
                {
                    "label": "Difficulties with Concentration",
                    "value": "concentration"
                },
                {
                    "label": "Feel Lightheaded or Faint",
                    "value": "presyncope"
                },
                {
                    "label": "Heart palpitations (fast or irregular heartbeat)",
                    "value": "palpitations"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = _.some(_.omit(data.lead_indications, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_lead_pnl",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.concat((data.lead_indications && data.lead_indications.none?['ni']:[]), _.keys(_.pickBy(_.omit(data.lead_contraindications, 'none'))))"
        },
        // amh-vca
        {
            "key": "patient_age_group",
            "type": "radio",
            "input": true,
            "label": "How old are you?",
            "values": [
                {
                    "label": "Under 35",
                    "value": "under_35"
                },
                {
                    "label": "35-39",
                    "value": "35_39"
                },
                {
                    "label": "40+",
                    "value": "40_plus"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sku0=='fem_pn_amh'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "amh_test_indication",
            "type": "selectboxes",
            "input": true,
            "label": "Do any of the following apply to you?",
            "values": [
                {
                    "label": "I am considering freezing my eggs (i.e., IVF, egg freezing)",
                    "value": "fertility_preservation"
                },
                {
                    "label": "I am undergoing IVF treatments",
                    "value": "ivf_treatments"
                },
                {
                    "label": "I have been unable to conceive after 12 months of trying",
                    "value": "under_35_trying_to_conceive",
                    "customConditional": "show = data.patient_age_group === 'under_35';"
                },
                {
                    "label": "I have been unable to conceive after 6 months of trying",
                    "value": "age_35_trying_to_conceive",
                    "customConditional": "show = data.patient_age_group === '35_39';"
                },
                {
                    "label": "I am planning on conceiving",
                    "value": "age_40_trying_to_conceive",
                    "customConditional": "show = data.patient_age_group === '40_plus';"
                },
                {
                    "label": "I plan on donating my eggs",
                    "value": "donating_eggs"
                },
                {
                    "label": "AMH testing is required by my fertility specialist",
                    "value": "required_by_specialist"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = !!data.patient_age_group",
            "optionsLabelPosition": "right"
        },
        {
            "key": "amh_health_status",
            "type": "selectboxes",
            "input": true,
            "label": "Please indicate if any of the following apply to you:",
            "values": [
                {
                    "label": "I am currently pregnant",
                    "value": "pregnant"
                },
                {
                    "label": "I have a history of ovarian cancer",
                    "value": "history_of_ovarian_cancer"
                },
                {
                    "label": "I am concerned I am experiencing a miscarriage",
                    "value": "concerned_about_miscarriage"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = _.some(_.omit(data.amh_test_indication, 'none')) && !data.amh_test_indication.none",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ci_amh",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Abdominal/Pelvic Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Breast discharge/leakage",
                    "value": "breast_discharge"
                },
                {
                    "label": "Heavier than normal vaginal bleeding",
                    "value": "heavier_vaginal_bleeding"
                },
                {
                    "label": "Spotting between your periods",
                    "value": "spotting_between_periods"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.amh_health_status && data.amh_health_status.none",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_fem_pn_amh",
            "type": "textfield",
            "input": true,
            "label": "Contraindication Keys",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "refreshOn": "ci_amh",
            "calculateValue": "v = _.get(data,'amh_test_indication.none') ? 'ni' : 0; ks = v ? 0 : _.concat(_.keys(_.pickBy(_.omit(data.ci_amh, 'none'))),_.keys(_.pickBy(_.omit(data.amh_health_status, 'none')))); value = v ? [v] : !_.isEmpty(ks) ? ks : !_.get(data, 'ci_amh.none') ? ['ic'] : []"
        },
        // vca-colon-cancer
        {
            "key": "current_age",
            "type": "radio",
            "input": true,
            "label": "Please select your age",
            "inline": false,
            "values": [
                {
                    "label": "50+",
                    "value": "50+"
                },
                {
                    "label": "49 or Younger",
                    "value": "<50"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.sku0=='colon_cancer_screening'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "family_history_colon_cancer",
            "type": "radio",
            "input": true,
            "label": "Do you have a 1st degree relative (i.e. mother, brother, father, sister) with a history of colon cancer?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.current_age == '<50'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "colonoscopy_rf",
            "type": "selectboxes",
            "input": true,
            "label": "Have you been diagnosed with any of the following medical conditions:",
            "values": [
                {
                    "label": "Crohn's Disease",
                    "value": "crohns"
                },
                {
                    "label": "Ulcerative Colitis",
                    "value": "ulcerative_colitis"
                },
                {
                    "label": "Familial Adenomatous Polyposis (FAP)",
                    "value": "melena_stools"
                },
                {
                    "label": "Lynch Syndrome",
                    "value": "lynch_syndrome"
                },
                {
                    "label": "Colon Cancer",
                    "value": "colon_cancer"
                },
                {
                    "label": "Oligopolyposis",
                    "value": "oligopolyposis"
                },
                {
                    "label": "Polyps requiring surveillance",
                    "value": "surveillance_polyps"
                },
                {
                    "label": "Juvenile Polyposis Syndrome",
                    "value": "juvenile_polyposis_syndrome"
                },
                {
                    "label": "Cowden syndrome",
                    "value": "cowden_syndrome"
                },
                {
                    "label": "Peutz-Jeghers syndrome",
                    "value": "peutz_jeghers_syndrome"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.family_history_colon_cancer == 'no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "cc_contraindications",
            "type": "selectboxes",
            "input": true,
            "label": "Are you experiencing any of the following symptoms:",
            "values": [
                {
                    "label": "Abdominal or Pelvic Pain",
                    "value": "abdo_pain"
                },
                {
                    "label": "Bloating",
                    "value": "bloating"
                },
                {
                    "label": "Black or tarry stools",
                    "value": "melena_stools"
                },
                {
                    "label": "Rectal bleeding (i.e. blood in stool)",
                    "value": "rectal_bleeding"
                },
                {
                    "label": "Chest pain or heaviness",
                    "value": "chest_pain"
                },
                {
                    "label": "Shortness of breath",
                    "value": "dyspnea"
                },
                {
                    "label": "Feel lightheaded or faint",
                    "value": "presyncope"
                },
                {
                    "label": "Heart palpitations (slow or fast heart beat)",
                    "value": "palpitations"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "customConditional": "show = data.family_history_colon_cancer=='yes'||(data.family_history_colon_cancer=='no' && _.some(data.colonoscopy_rf))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "ciks_colon_cancer_screening",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": false,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = (data.current_age == '<50' && !_.some(_.omit(data.colonoscopy_rf, 'none')) && data.family_history_colon_cancer=='no') ? ['no_indications'] : _.keys(_.omit(data.cc_contraindications, 'none'))",
            "refreshOnChange": true
        },
    ]
},
{
    "title": "Preferences",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "use_insurance",
            "type": "radio",
            "input": true,
            "label": "Do you have health insurance (e.g. OHIP) and wish to use it?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                  "label": "No",
                  "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "defaultValue": "yes",
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
        {
            "key": "desired_test_period",
            "type": "radio",
            "input": true,
            "label": "How often are you interested in testing?",
            "values": [
                {
                    "label": "Not sure or based on doctor’s recommendation",
                    "value": "not_sure"
                },
                {
                    "label": "Monthly",
                    "value": "month"
                },
                {
                    "label": "Every 3 months",
                    "value": "3months"
                },
                {
                    "label": "Every 6 months",
                    "value": "6months"
                },
                {
                    "label": "Yearly",
                    "value": "year"
                },
                {
                    "label": "Once",
                    "value": "once"
                }
            ],
            // "defaultValue": "once",
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = !!data.use_insurance"
        },
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from lab testing, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "STD/STI, BV, or UTI testing or treatment",
                    "value": "std"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                  "label": "Hair regrowth",
                  "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Prescription renewal",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "validate": {"required": true},
            // "defaultValue": "none",
            "tableView": true,
            "customConditional": "show = !!data.use_insurance && !!data.desired_test_period"
        },
    ]
  },
  {
    "title": "Result",
    "type": "panel",
    "key": "page3",
    "components": [
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"met_pn_all"             : {"n":"Metabolic Panel"       ,"a":"metabolic testing"},
"longevity_bloodwork"    : {"n":"Longevity Panel"       ,"a":"metabolic testing"},
"mens_sperm_testing"     : {"n":"Sperm Testing"         ,"a":"male fertility testing"},
"hair_loss_bloodwork"    : {"n":"Hair Loss Panel"       ,"a":"hair loss"},
"met_pn_thy"             : {"n":"Thyroid Panel"         ,"a":"thyroid testing"},
"celiac_pnl"             : {"n":"Celiac Panel"          ,"a":"celiac testing"},
"immunity_pnl"           : {"n":"Work & School Panel"   ,"a":"lab testing"},
"fem_iron_deficiency"    : {"n":"Anemia Panel"          ,"a":"anemia testing"},
"mens_psa_testing"       : {"n":"PSA Panel"             ,"a":"PSA testing"},
"vit_d_pnl"              : {"n":"Vitamin D Panel"       ,"a":"vitamin D testing"},
"pre_op_bloodwork"       : {"n":"Pre-Operative Panel"   ,"a":"pre-operative lab tests"},
"uric_pnl"               : {"n":"Uric Acid Panel"       ,"a":"uric acid testing"},
"inr_testing"            : {"n":"INR Testing"           ,"a":"INR testing"},
"rabies_panel"           : {"n":"Rabies Titre"          ,"a":"rabies testing"},
"lead_pnl"               : {"n":"Lead Level Panel"      ,"a":"lead testing"},
"igra_test"              : {"n":"TB IGRA Gold Test"     ,"a":"TB IGRA testing"},
"fem_pn_hcg"             : {"n":"Pregnancy Panel"       ,"a":"pregnancy testing"},
"chest_x_ray_panel"      : {"n":"Chest X-ray"           ,"a":"chest X-raying"},
"met_pn_all_dm"          : {"n":"Diabetes Panel"        ,"a":"diabetes testing"},
"isocyanate_test"        : {"n":"Isocyanate Test"       ,"a":"isocyanate testing"},
"fem_pn_amh"             : {"n":"Female Fertility Panel","a":"fertility testing"},
"colon_cancer_screening" : {"n":"Colon Cancer Screening","a":"cancer screening"},
"hep_a_igm_pnl"          : {"n":"Hepatitis A IgM Screening"    ,"a":"hepatitis A"},
"bmd_testing_women"      : {"n":"Bone Mineral Density Testing" ,"a":"bone mineral testing"},
"h_pylori"               : {"n":"H. Pylori Testing & Treatment","a":"H. Pylori"},
"preven_nmr_lipid"       : {"n":"NMR Lipid Profile (Dynacare Only)","a":"NMR lipid profile testing"},
            }
        },
        {
            "key": "s2a",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
anti_fungal_medication    : ["FUNGAL-SCRAPING"],
bmd_testing_women         : ["BMD-TEST"],
celiac_pnl                : ["TTG-IGA", "IGA"],
chest_x_ray_panel         : ["CXR"],
colon_cancer_screening    : ["COLON-CANCER"],
ed_pn_bloodwork           : ["CBC", "FBG", "HBA1C", "LP", "CN", "LIPOPROTEIN-A", "APO-B", "B12", "D25", "B9"],
fem_iron_deficiency       : ["CBC", "FERRITIN"],
fem_pn_amh                : ["AMH"],
fem_pn_uti                : ["UTI"],
fem_pn_vswb               : ["VSWB"],
h_pylori                  : ["H-PYLORI-SEROLOGY", "H-PYLORI-UREA"],
hair_loss_bloodwork       : ["CBC", "FERRITIN", "TSH", "D25", "B12", "B9", "SELENIUM", "ZINC", "VDRL"],
hep_a_igm_pnl             : ["HEPA-IGM"],
igra_test                 : ["IGRA"],
immunity_pnl              : ["MEASLES", "MUMPS", "RUBELLA", "VZV", "HEPA-SAB", "HEPB-SAB", "HCV-AB"],
inr_testing               : ["INR"],
isocyanate_test           : ["ISOCYANATE"],
lead_pnl                  : ["LEAD"],
longevity_bloodwork       : ["FBG", "LP", "INS", "HS-CRP", "HBA1C", "APO-B", "LIPOPROTEIN-A", "CYSTATIN-C", "D25"],
mens_libido               : ["TT"],
mens_psa_testing          : ["PSA"],
mens_sperm_testing        : ["SEMEN-FERTILITY", "SEMEN-VASECTOMY"],
met_pn_all                : ["CBC", "FBG", "HBA1C", "LP", "CN", "LIPOPROTEIN-A", "APO-B", "B12", "D25", "B9"],
met_pn_all_dm             : ["Q-DM-MON", "HBA1C", "LP", "CN", "UACR"],
met_pn_thy                : ["TSH", "FT4", "FT3", "RT3"],
pre_op_bloodwork          : ["PRE-OP"],
preven_nmr_lipid          : ["NMR"],
rabies_panel              : ["RABIES"],
referral_vasectomy        : ["VASECTOMY"],
uric_pnl                  : ["URIC"],
vit_d_pnl                 : ["D25"],
            }
        },
        {
            "key": "a2i",
            "type": "textfield",
            "input": true,
            "label": "Assay to insured dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"FUNGAL-SCRAPING"   : {"anti_fungal_medication": true},
"BMD-TEST"          : {"bmd_testing_women": true},
"TTG-IGA"           : {"celiac_pnl": true},
"IGA"               : {"celiac_pnl": true},
"CXR"               : {"chest_x_ray_panel": false},
"COLON-CANCER"      : {"colon_cancer_screening": true},
"CBC"               : {"ed_pn_bloodwork": true, "fem_iron_deficiency": true, "hair_loss_bloodwork": true, "met_pn_all": true},
"FBG"               : {"ed_pn_bloodwork": true, "longevity_bloodwork": false, "met_pn_all": true},
"HBA1C"             : {"ed_pn_bloodwork": true, "longevity_bloodwork": false, "met_pn_all": true, "met_pn_all_dm": true},
"LP"                : {"ed_pn_bloodwork": true, "longevity_bloodwork": false, "met_pn_all": true, "met_pn_all_dm": true},
"CN"                : {"ed_pn_bloodwork": true, "met_pn_all": true, "met_pn_all_dm": true},
"LIPOPROTEIN-A"     : {"ed_pn_bloodwork": true, "longevity_bloodwork": false, "met_pn_all": true},
"APO-B"             : {"ed_pn_bloodwork": true, "longevity_bloodwork": false, "met_pn_all": true},
"B12"               : {"ed_pn_bloodwork": false, "hair_loss_bloodwork": false, "met_pn_all": false},
"D25"               : {"ed_pn_bloodwork": false, "hair_loss_bloodwork": false, "longevity_bloodwork": false, "met_pn_all": false, "vit_d_pnl": false},
"B9"                : {"ed_pn_bloodwork": false, "hair_loss_bloodwork": false, "met_pn_all": false},
"FERRITIN"          : {"fem_iron_deficiency": true, "hair_loss_bloodwork": true},
"AMH"               : {"fem_pn_amh": false},
"UTI"               : {"fem_pn_uti": true},
"VSWB"              : {"fem_pn_vswb": true},
"H-PYLORI-SEROLOGY" : {"h_pylori": true},
"H-PYLORI-UREA"     : {"h_pylori": false},
"TSH"               : {"hair_loss_bloodwork": true, "met_pn_thy": true},
"SELENIUM"          : {"hair_loss_bloodwork": false},
"ZINC"              : {"hair_loss_bloodwork": false},
"VDRL"              : {"hair_loss_bloodwork": true},
"HEPA-IGM"          : {"hep_a_igm_pnl": true},
"IGRA"              : {"igra_test": false},
"MEASLES"           : {"immunity_pnl": true},
"MUMPS"             : {"immunity_pnl": true},
"RUBELLA"           : {"immunity_pnl": true},
"VZV"               : {"immunity_pnl": true},
"HEPA-SAB"          : {"immunity_pnl": true},
"HEPB-SAB"          : {"immunity_pnl": true},
"HCV-AB"            : {"immunity_pnl": true},
"INR"               : {"inr_testing": true},
"ISOCYANATE"        : {"isocyanate_test": false},
"LEAD"              : {"lead_pnl": true},
"INS"               : {"longevity_bloodwork": false},
"HS-CRP"            : {"longevity_bloodwork": false},
"CYSTATIN-C"        : {"longevity_bloodwork": false},
"TT"                : {"mens_libido": true},
"PSA"               : {"mens_psa_testing": false},
"SEMEN-FERTILITY"   : {"mens_sperm_testing": true},
"SEMEN-VASECTOMY"   : {"mens_sperm_testing": true},
"Q-DM-MON"          : {"met_pn_all_dm": true},
"UACR"              : {"met_pn_all_dm": true},
"FT4"               : {"met_pn_thy": false},
"FT3"               : {"met_pn_thy": false},
"RT3"               : {"met_pn_thy": false},
"PRE-OP"            : {"pre_op_bloodwork": true},
"NMR"               : {"preven_nmr_lipid": false},
"RABIES"            : {"rabies_panel": true},
"VASECTOMY"         : {"referral_vasectomy": true},
"URIC"              : {"uric_pnl": true},
            }
        },
        {
            "key": "td",
            "type": "textfield",
            "input": true,
            "label": "Assay to name & price dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"AMH"               : {p: 70, n:"Anti-Müllerian Hormone (AMH)"},
"APO-B"             : {p: 30, n:"Apolipoprotein B"},
"B12"               : {p: 20, n:"Vitamin B12"},
"B9"                : {p: 27, n:"Vitamin B9 (Folate)"},
"BMD-TEST"          : {p: 10, n:"Bone Mineral Density X-ray"},
"CBC"               : {p:  8, n:"Complete Blood Count (CBC)"},
"CN"                : {p:  3, n:"Creatinine (eGFR)"},
"COLON-CANCER"      : {p: 50, n:"Colonoscopy / Fecal Immunochemical Test (FIT) "},
"CXR"               : {p: 50, n:"Chest X-Ray"},
"CYSTATIN-C"        : {p: 50, n:"Cystatin-C"},
"D25"               : {p: 41, n:"Vitamin D"},
"FBG"               : {p:  3, n:"Blood Glucose"},
"FERRITIN"          : {p: 20, n:"Ferritin"},
"FT3"               : {p: 15, n:"Free T3"},
"FT4"               : {p: 15, n:"Free T4"},
"FUNGAL-SCRAPING"   : {p: 10, n:"Fungal Nail Clipping (LifeLabs Only)"},
"H-PYLORI-SEROLOGY" : {p:100, n:"Serology Blood Test (Before Treatment)"},
"H-PYLORI-UREA"     : {p: 90, n:"Breath Test (After Treatment)"},
"HBA1C"             : {p: 11, n:"HbA1c"},
"HCV-AB"            : {p: 12, n:"Hepatitis C (Anti-HCV Antibody)"},
"HEPA-IGM"          : {p: 12, n:"Hepatitis A IgM (IgM anti-HAV)"},
"HEPA-SAB"          : {p: 12, n:"Hepatitis A (Antibody/Antigen)"},
"HEPB-SAB"          : {p: 12, n:"Hepatitis B (Surface Antibody/Antigen)"},
"HS-CRP"            : {p:  9, n:"High-sensitivity C-reactive protein (hsCRP)"},
"IGA"               : {p:  9, n:"Immunoglobulin A (IgA)"},
"IGRA"              : {p: 40, n:"Interferon-Gamma Release Assay (IGRA)"},
"INR"               : {p:  9, n:"INR Testing"},
"INS"               : {p: 20, n:"Fasting Insulin"},
"ISOCYANATE"        : {p: 50, n:"Isocyanate HDI"},
"LEAD"              : {p: 25, n:"Lead Levels (Whole Blood)"},
"LIPOPROTEIN-A"     : {p: 12, n:"Lipoprotein(a)"},
"LP"                : {p: 20, n:"Lipid Profile (LDL, HDL, Triglycerides)"},
"MEASLES"           : {p: 16, n:"Measles"},
"MUMPS"             : {p:  9, n:"Mumps"},
"NMR"               : {p:180, n:"NMR Lipid Profile"},
"PSA"               : {p: 37, n:"Prostate Specific Antigen (PSA)"},
"RABIES"            : {p:130, n:"Rabies Immunity"},
"RT3"               : {p: 58, n:"Reverse T3"},
"RUBELLA"           : {p: 12, n:"Rubella"},
"SELENIUM"          : {p: 68, n:"Selenium"},
"SEMEN-FERTILITY"   : {p: 25, n:"Semen/Sperm Fertility Analysis"},
"SEMEN-VASECTOMY"   : {p: 25, n:"Semen Analysis (After Vasectomy)"},
"TSH"               : {p: 20, n:"Thyroid Stimulating Hormone (TSH)"},
"TTG-IGA"           : {p: 10, n:"Tissue transglutaminase IgA (tTg-IgA)"},
"UACR"              : {p:  5, n:"Urine Albumin:Creatinine Ratio"},
"URIC"              : {p: 20, n:"Uric Acid Levels"},
"VDRL"              : {p: 18, n:"Syphilis"},
"VZV"               : {p: 18, n:"Varicella"},
"ZINC"              : {p: 68, n:"Zinc"},
// "PRE-OP"            : {p: 10, n:"Pre-operative Bloodwork"},
// "Q-DM-MON"          : {p: 10, n:"Diabetes Quarterly Monitoring"},
            }
        },
        {
            "key": "final_product",
            "type": "textfield",
            "input": true,
            "label": "Final Product:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "s=data.sku0; n=_.get(data.sd,s+'.n'); a=_.get(data.sd,s+'.a'); t=_.upperFirst(n); value = !s ? null : {n:t, p:49, tp:'once', d: `Messaging consultation about ${a}.`}"
        },
        {
            "key": "display_rejected_html",
            "html": "<h3 class='text-red'>You're ineligible for monitoring at this time. Please seek in-person care with a physician to discuss your concerns.</h3>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = !data.showSubmit && !_.startsWith(data.sku0, 'adh_')"
        },
        {
            "key": "all_keys",
            "type": "textfield",
            "input": true,
            "label": "All keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "refreshOn": "sku0",
            "defaultValue": [],
            "calculateValue": "value = !data.sku0 ? [] : _.get(data.s2a, data.sku0, [])"
        },
        {
            "key": "ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "refreshOn": "all_keys,use_insurance",
            "defaultValue": [],
            "calculateValue": "value = (!data.all_keys||_.isEmpty(data.all_keys)||data.use_insurance=='no') ? [] : _.filter(data.all_keys, k=>_.get(data.a2i,k+'.'+data.sku0))"
        },
        {
            "key": "non_ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "Non-OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "refreshOn": "ohip_keys",
            "defaultValue": [],
            "calculateValue": "value = (!data.all_keys||_.isEmpty(data.all_keys)||!data.ohip_keys) ? [] : _.filter(data.all_keys, k=>!data.ohip_keys.includes(k))"
        },
        {
            "key": "wizard-blurb",
            "type": "wizard-blurb",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "data",
            "customConditional": "show = data.sku0 && _.isEmpty(_.compact(data.idrs))"
        },
        {
            "key": "faq_title",
            "html": "<h4>FAQs</h4><hr>",
            "type": "content",
            "customConditional": "show = !!data.showSubmit",
        },
        {
            "key": "faq",
            "type": "wizard-faq",
            "customConditional": "show = !!data.showSubmit",
            "rows": [
                {
                    "q": "How can I reimburse with my private insurance or Health Spending Account (HSA)?",
                    "p": "<p>Most insurance plans cover online/virtual medical consultation with a physician. We will send you a receipt for your consultation or subscription, which you can use to seek reimbursement from your private insurance or HSA.</p><p>If you require specific details just email <NAME_EMAIL> for a detailed receipt suitable for your benefits claims.</p>"
                },
                {
                    "q": "How do I my get lab results? How long does it take?",
                    "p": "<p>All lab results are available via our online portal, unlike old-school doctor's offices no paper or phones are required.</p><p>We have a custom system that constantly monitors when lab results are ready and alerts you via email.</p><p>We have the fastest turnaround times in Ontario.</p>"
                },
                {
                    "q": "Are video visits required?",
                    "p": "<p><strong>No video visit is required;</strong> a provider will review your information 100% online. If prescribed, you’ll get online access to message your provider.</p>"
                },
                {
                    "q": "Will my current doctor find out about TeleTest? Will this affect roster status?",
                    "p": "<p><strong>No.</strong> TeleTest appointments are not OHIP-funded &amp; will not affect roster status.</p>"
                }
            ]
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0 && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = !!data.showSubmit"
        }
    ]
}
];

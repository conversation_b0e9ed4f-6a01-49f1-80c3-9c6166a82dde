var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Start Your Weight Loss Journey</h1><p>The following questionnaire will:<ol><li>Provide a recommendation based on 3-5 quick questions</li><li>Allow you to choose specific treatments you may want</li><li>Determine total cost</li></ol>Note: insurance/OHIP is not required, however it can reduce out-of-pocket costs.</p>",
            "type": "content"
        },
    {
        "key": "weight_loss_goals",
        "type": "radio",
        "input": true,
        "label": "What's your weight loss goal?",
        "values": [
            {
                "label": "Losing 1-15 lbs",
                "value": "losing_1_15"
            },
            {
                "label": "Losing 16-50 lbs",
                "value": "losing_16_50"
            },
            {
                "label": "Losing 51+ lbs",
                "value": "losing_51_plus"
            },
            {
                "label": "Not sure, I just need to lose weight",
                "value": "not_sure"
            }
        ],
        // "defaultValue": "moderate_loss",
        "validate": { "required": true },
        "tableView": true,
        "optionsLabelPosition": "right"
    },

        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"weight_loss_medication_start": {"n":"Weight Loss Medication Start","d":"Messaging consultation about starting a weight loss medication. Requires baseline bloodwork to be completed before a prescription can be issued.","p":4900,"tp":"once"},
"weight_loss_medication_renewal": {"n":"Weight Loss Medication Renewal","d":"Messaging consultation about renewing your weight loss medication for 3 months. For individuals currently on weight loss therapy.","p":2900,"tp":"once"},
"wl_glp_diet": {"n":"GLP-1 Diet Program","d":"Weight Loss Program including appointments, medication, and meal delivery.","p":69000,"tp":"once"},
"weight_loss_medication_authorization": {"n":"Insurance Authorization Form","d":"Complete insurance paperwork to authorize your request for a GLP-1 prescription (i.e. Wegovy, Saxenda, Ozempic)","p":8900,"tp":"once"},
            }
        },
        {
            "key": "final_product",
            "type": "textfield",
            "input": true,
            "label": "Final Product:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "fp = data.sku0 ? data.sd[data.sku0] : data.sd['weight_loss_medication_start']; value = {n:fp.n, p:fp.p, d:fp.d, tp:fp.tp}"
        },
        {
            "key": "sku0",
            "type": "textfield",
            "input": true,
            "label": "SKU0:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = 'weight_loss_medication_start';"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Rx Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": ""
        },

        {
            "key": "glp1_experience",
            "type": "radio",
            "input": true,
            "label": "Are you currently or have you ever taken a GLP-1 medication?",
            "inline": false,
            "values": [
                {
                    "label": "I am currently taking a GLP-1 medication",
                    "value": "currently_taking"
                },
                {
                    "label": "I have taken a GLP-1 medication in the past but I'm not currently",
                    "value": "taken_in_past"
                },
                {
                    "label": "I have never taken a GLP-1 medication",
                    "value": "never_taken"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = !!data.weight_loss_goals",
            "optionsLabelPosition": "right"
        },
        {
            "key": "glp1_info",
            "html": "<p class='text-muted small'>GLP-1s can include compounded semaglutide, compounded tirzepatide, Ozempic, Wegovy, Mounjaro and Zepbound.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = !!data.weight_loss_goals"
        },
        {
            "key": "weight_loss_challenges",
            "type": "selectboxes",
            "input": true,
            "label": "What's the hardest thing about losing weight for you?",
            "inline": false,
            "values": [
                {
                    "label": "Controlling hunger or cravings",
                    "value": "hunger_cravings"
                },
                {
                    "label": "Staying motivated",
                    "value": "staying_motivated"
                },
                {
                    "label": "Finding time to exercise",
                    "value": "time_exercise"
                },
                {
                    "label": "Staying on top of my diet",
                    "value": "diet_adherence"
                },
                {
                    "label": "Cost of weight loss programs or medication",
                    "value": "cost"
                },
                {
                    "label": "Medication side effects",
                    "value": "medication_side_effects"
                },
                {
                    "label": "Medical conditions that affect weight loss",
                    "value": "medical_conditions"
                },
                {
                    "label": "Increased appetite caused by medications",
                    "value": "medication_appetite"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = !!data.glp1_experience",
            "optionsLabelPosition": "right"
        },
        {
            "key": "challenges_note",
            "html": "<p class='text-muted small'>Select all that apply.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = !!data.glp1_experience"
        },
        {
            "key": "weight_loss_challenges_other",
            "type": "textfield",
            "input": true,
            "label": "Please specify:",
            "placeholder": "Enter details...",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.weight_loss_challenges && data.weight_loss_challenges.other"
        },
        {
            "key": "stopped_programs",
            "type": "selectboxes",
            "input": true,
            "label": "Have you ever stopped a weight loss program or prescription before reaching your goal? If so, why?",
            "inline": false,
            "values": [
                {
                    "label": "I wasn't seeing results fast enough",
                    "value": "slow_results"
                },
                {
                    "label": "I couldn't stick with it",
                    "value": "couldnt_stick"
                },
                {
                    "label": "It became too expensive",
                    "value": "too_expensive"
                },
                {
                    "label": "I experienced side effects",
                    "value": "side_effects"
                },
                {
                    "label": "Life got in the way (stress, travel, family obligations)",
                    "value": "life_got_in_way"
                },
                {
                    "label": "No, I'm usually consistent",
                    "value": "usually_consistent"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.weight_loss_challenges && Object.values(data.weight_loss_challenges).some(v => v)",
            "optionsLabelPosition": "right"
        },
        {
            "key": "stopped_programs_other",
            "type": "textfield",
            "input": true,
            "label": "Please specify:",
            "placeholder": "Enter details...",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.stopped_programs && data.stopped_programs.other"
        },
        {
            "key": "program_support",
            "type": "selectboxes",
            "input": true,
            "label": "What would make it easier for you to stick with a weight loss program?",
            "inline": false,
            "values": [
                {
                    "label": "Personalized recommendations for nutrition and movement",
                    "value": "personalized_recommendations"
                },
                {
                    "label": "Convenient meal options (meal replacements, meal plans)",
                    "value": "convenient_meals"
                },
                {
                    "label": "Digital tools (tracking apps, coaching)",
                    "value": "digital_tools"
                },
                {
                    "label": "Personalized dosage schedule that would help address side effects",
                    "value": "personalized_dosage"
                },
                {
                    "label": "Stronger community support",
                    "value": "community_support"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.stopped_programs && Object.values(data.stopped_programs).some(v => v)",
            "optionsLabelPosition": "right"
        },
        {
            "key": "support_note",
            "html": "<p class='text-muted small'>Select all that apply.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.stopped_programs && Object.values(data.stopped_programs).some(v => v)"
        },
        {
            "key": "program_support_other",
            "type": "textfield",
            "input": true,
            "label": "Please specify:",
            "placeholder": "Enter details...",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.program_support && data.program_support.other"
        },
        {
            "key": "knows_medication",
            "type": "radio",
            "input": true,
            "label": "Do you have a specific weight loss medication in mind?",
            "inline": false,
            "values": [
                {
                    "label": "Not yet, I'm looking for a recommendation",
                    "value": "no"
                },
                {
                    "label": "Yes, I already have something in mind",
                    "value": "yes"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.program_support && Object.values(data.program_support).some(v => v)",
            "optionsLabelPosition": "right"
        },
        {
            "key": "preferred_medication",
            "type": "radio",
            "input": true,
            "label": "Which medication are you interested in?",
            "inline": false,
            "values": [
                {
                    "label": "Ozempic (Semaglutide)",
                    "value": "ozempic"
                },
                {
                    "label": "Wegovy (Semaglutide)",
                    "value": "wegovy"
                },
                {
                    "label": "Rybelsus (Semaglutide)",
                    "value": "rybelsus"
                },
                {
                    "label": "Mounjaro (Tirzepatide)",
                    "value": "mounjaro"
                },
                {
                    "label": "Zepbound (Tirzepatide)",
                    "value": "zepbound"
                },
                {
                    "label": "Saxenda (Liraglutide)",
                    "value": "saxenda"
                },
                {
                    "label": "Orlistat (Xenical)",
                    "value": "orlistat"
                },
                {
                    "label": "Contrave (Naltrexone/Bupropion)",
                    "value": "contrave"
                },
                {
                    "label": "Qsymia (Phentermine/Topiramate)",
                    "value": "qsymia"
                },
                {
                    "label": "Phentermine (Adipex-P)",
                    "value": "phentermine"
                },
                {
                    "label": "Other/Not listed",
                    "value": "other"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.knows_medication == 'yes'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "preferred_medication_other",
            "type": "textfield",
            "input": true,
            "label": "Please specify:",
            "placeholder": "Enter medication name...",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.preferred_medication == 'other'"
        },
        {
            "key": "delivery_preference",
            "type": "radio",
            "input": true,
            "label": "Do you have a preference for how the medication is taken?",
            "inline": false,
            "values": [
                {
                    "label": "Oral medication (pills/tablets)",
                    "value": "oral"
                },
                {
                    "label": "Injection (weekly shots)",
                    "value": "injection"
                },
                {
                    "label": "Whatever is most effective",
                    "value": "most_effective"
                },
                {
                    "label": "No preference / Whatever the doctor recommends",
                    "value": "no_preference"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.knows_medication == 'no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "pharmacy_preference",
            "type": "radio",
            "input": true,
            "label": "How would you prefer to receive your prescription?",
            "inline": false,
            "values": [
                {
                    "label": "Home delivery with free shipping",
                    "value": "delivery"
                },
                {
                    "label": "I already have a pharmacy I prefer to use",
                    "value": "existing_pharmacy"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = !!data.knows_medication",
            "optionsLabelPosition": "right"
        }

    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "ciks_wl_glp1_sub",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = []", // No contraindications for this basic version
        },
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from weight loss, is there another area of your health you'd like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                    "label": "Hair regrowth",
                    "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                    "label": "Mental health",
                    "value": "mental_health"
                },
                {
                    "label": "Non-weight-loss prescription renewal",
                    "value": "rx_renewal"
                },
                {
                    "label": "Testosterone support",
                    "value": "testosterone"
                },
                {
                    "label": "Birth control or cycle management",
                    "value": "birth_control"
                },
                {
                    "label": "None of these",
                    "value": "none"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Regrow Your Hair</h1><p class='fst-italic'>Note: currently only for men.</p>",
            "type": "content"
        },
    {
        "key": "hair_loss_goals",
        "type": "radio",
        "input": true,
        "label": "Which best represents your hair loss and goals?",
        "values": [
            {
                "label": "Receding hairline, want to slow its progress",
                "value": "receding_hairline"
            },
            {
                "label": "Experiencing hair loss, exploring options",
                "value": "exploring_options"
            },
            {
                "label": "Experiencing hair loss, ready to start treatment ASAP",
                "value": "treatment_asap"
            },
            {
                "label": "No hair loss yet, want to get ahead of it",
                "value": "get_ahead"
            },
            {
                "label": "Other",
                "value": "other"
            }
        ],
        // "defaultValue": "receding_hairline",
        "validate": { "required": true },
        "tableView": true,
        "optionsLabelPosition": "right"
    },
        {
            "key": "topic_other",
            "type": "textarea",
            "input": true,
            "label": "Please state what you’re interested in:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.hair_loss_goals=='other'"
        },
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"mh_hl_dut"   : {"n":"Oral Dutasteride"  ,"d":"A stronger once-a-day pill is clinically proven to increase regrowth or reduce hair loss."},
"mh_hl_pill"  : {"n":"Oral Finasteride"  ,"d":"This once-a-day pill is clinically proven to increase regrowth or reduce hair loss. Simple."},
"mh_hl_serum" : {"n":"Rx Hair Loss Serum","d":"Once-a-day serum with precision dropper for better access to the scalp."},
"mh_hl_spray" : {"n":"Rx Hair Loss Spray","d":"A quick-drying, once-a-day spray with finasteride and minoxidil."},
            }
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = 'mh_hl_pill'",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Rx Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": ""
        },
        // start hair loss rfs from vca-rx-finasteride
        {
            "key": "currently_taking_finasteride",
            "type": "radio",
            "input": true,
            "label": "Have you ever been on finasteride/dutasteride, or are you currently taking it?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            // "defaultValue": "yes",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = !!data.hair_loss_goals",
            "optionsLabelPosition": "right"
        },
        {
            "key": "sudden_hair_loss",
            "type": "radio",
            "input": true,
            "label": "Have you experienced a sudden loss of hair in the last 6 months?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.currently_taking_finasteride=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "renewal_note",
            "html": "<p class='text-red'>We offer renewals or new prescription starts for chronic hair loss changes (changes observed over a period longer than 6 months). Sudden onset hair loss can be associated with other medical conditions including alopecia aerata or telogen effluvium, which do not respond to finasteride or dutasteride.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='yes'"
        },
        // {
        //     "key": "photo_new",
        //     "html": "<p class='text-green'>TeleTest physicians require a photo of your scalp (i.e. crown, temples, forehead) to initiate a new prescription of finasteride or dutasteride.</p>",
        //     "type": "content",
        //     "input": true,
        //     "tableView": false,
        //     "clearOnHide": false,
        //     "customConditional": "show = data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='no'"
        // },
        {
            "key": "ciks_mh_hl_sub",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.concat((data.currently_taking_finasteride=='no' && data.sudden_hair_loss=='yes' ? ['no_new_rx_sudden_hl'] : []))",
        },
    ]
},
{
    "title": "Preferences",
    "type": "panel",
    "key": "page1",
    "components": [
    {
        "key": "form_factor",
        "type": "radio",
        "input": true,
        "label": "Which kind of daily treatment do you prefer?",
        "values": [
            {
                "label": "Pills or chews",
                "value": "oral"
            },
            {
                "label": "Topical sprays or serums",
                "value": "topical"
            },
            {
                "label": "No preference",
                "value": "no_preference"
            }
        ],
        // "defaultValue": "topical",
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))",
        "optionsLabelPosition": "right"
    },
    {
        "key": "topical_preference",
        "type": "radio",
        "input": true,
        "label": "Would you prefer a spray or a serum?",
        "values": [
            {
                "label": "Spray",
                "value": "spray"
            },
            {
                "label": "Serum",
                "value": "serum"
            },
            {
                "label": "No preference",
                "value": "no_preference"
            }
        ],
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.form_factor == 'topical'",
        "optionsLabelPosition": "right"
    },
    {
        "key": "oral_preference",
        "type": "radio",
        "input": true,
        "label": "Would you prefer taking a chew or a pill?",
        "values": [
            {
                "label": "Chew",
                "value": "chew"
            },
            {
                "label": "Pill",
                "value": "pill"
            },
            {
                "label": "No preference",
                "value": "no_preference"
            }
        ],
        // "defaultValue": "chew",
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.form_factor == 'oral'",
        "optionsLabelPosition": "right"
    },
        {
            "key": "chew_note",
            "html": "<p class='text-green'>We don't currently offer chews but we're working on it!</p><p class='text-green'>You can start on pills and switch to chews as soon as we have it ready.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.oral_preference == 'chew'"
        },
        {
            "key": "topical_note",
            "html": "<p class='text-green'>We don't currently offer topicals like sprays or serums but we're working on it!</p><p class='text-green'>You can start on pills and switch to a topical as soon as we have it ready.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = !!data.topical_preference"
        },
    {
        "key": "fin_or_dut",
        "type": "radio",
        "input": true,
        "label": "Do you have a preference for either finasteride or dutasteride?",
        "values": [
            {
                "label": "Finasteride",
                "value": "fin"
            },
            {
                "label": "Dutasteride",
                "value": "dut"
            },
            {
                "label": "Not sure or based on doctor’s recommendation",
                "value": "not_sure"
            }
        ],
        // "defaultValue": "fin",
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = !!data.oral_preference",
        "optionsLabelPosition": "right"
    },
    {
        "key": "desired_rx_period",
        "type": "radio",
        "input": true,
        "label": "How many months supply of medication would you prefer if subscribed?",
        "values": [
            {
                "label": "1 month",
                "value": "month"
            },
            {
                "label": "3 months",
                "value": "3months"
            },
            {
                "label": "6 months",
                "value": "6months"
            },
            {
                "label": "One year",
                "value": "year"
            }
        ],
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))",
        "optionsLabelPosition": "right"
    },
    {
        "key": "supply_period_note",
        "html": "<p class='text-green'>We're currently only offering 3-month supplies, but we're working on other options!</p>",
        "type": "content",
        "input": true,
        "tableView": false,
        "clearOnHide": false,
        "customConditional": "show = !!data.desired_rx_period"
    },
    {
        "key": "sub_period",
        "type": "textfield",
        "input": true,
        "label": "Subscription Period:",
        "hidden": true,
        "disabled": true,
        "multiple": false,
        "tableView": true,
        "clearOnHide": false,
        "calculateValue": "value = '3months'",
        "refreshOnChange": true
    },
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from hair, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Prescription renewal for something else",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            // "defaultValue": "none",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

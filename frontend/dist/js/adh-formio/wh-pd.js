var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Delay Your Period</h1>TeleTest physicians can prescribe medications like:<ul><li>NETA (Norethindrone Acetate)</li><li>MPA (Medroxyprogesterone Acetate)</li></ul><p>The following questionnaire will determine your eligibility.</p><p>Note: insurance/OHIP is not required.</p>",
            "type": "content"
        },
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"fem_pn_ella"                   : {"new":  true, "n": "emergency contraception"},
"fem_pn_ocp"                    : {"new":  true, "n": "birth control"},
"fem_pn_suppress_menses"        : {"new":  true, "n": "period delay"},
            }
        },
        {
            "key": "final_product",
            "type": "textfield",
            "input": true,
            "label": "Final Product:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = {n:'Period Delay Medication', p:49, tp:'once', d: `TeleTest physicians can prescribe medications like:<ul><li>NETA (Norethindrone Acetate)</li><li>MPA (Medroxyprogesterone Acetate)</li></ul>These medications help prevent menstrual bleeding while you're away or at your event, and your cycles will resume once you stop taking the medication.`}"
        },
        {
            "key": "sku0",
            "type": "textfield",
            "input": true,
            "label": "SKU0:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = 'fem_pn_suppress_menses'"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Rx Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "v = !data.sku0 ? 'no_sku0' : _.some(['adh_','tbd_'], x=>_.startsWith(data.sku0, x)) ? data.sku0 : 0; ks = v ? 0 : _.compact(_.get(data, `ciks_${data.sku0}`, _.keys(_.pickBy(_.omit(_.get(data, `ci_${data.sku0}`), 'none'))))); value = v ? [v] : !_.isEmpty(ks) ? (_.some(['ci','ni','ic'], x=>_.includes(ks,x)) ? ks :_.concat('ci', ks)) : _.get(data, `ci_${data.sku0}.none`)===false ? ['ic'] : []"
        },
        {
            "key": "rx_idr_html",
            "html": "<div class='mt-3 text-red'><h3 class='text-red'>We’re unable to renew your prescription at this time.</h3><p>We are currently not able to renew prescriptions for {{data.sku0=='tbd_mh'?`mental health`:``}}{{data.sku0=='tbd_iud'?`IUDs`:``}}{{data.sku0=='tbd_cdsa'?`controlled substances`:``}}{{data.sku0=='tbd_serm'?`SERMs`:``}}{{data.sku0=='tbd_prep'?`PrEP`:``}}{{data.sku0=='tbd_gaba'?`Gabpentin or Pregabalin`:``}}.</p></div>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = data.rx && data.sku0.startsWith('tbd_')"
        },
        // OCP
        {
            "key": "ci_fem_pn_ocp",
            "type": "selectboxes",
            "input": true,
            "label": "Please select all that apply:",
            "values": [
                {
                    "label": "Currently or possibly pregnant",
                    "value": "pregnant"
                },
                {
                    "label": "Had a baby within the last 42 days",
                    "value": "recent_baby"
                },
                {
                    "label": "High Blood Pressure",
                    "value": "dx_htn"
                },
                {
                    "label": "Deep Vein Thrombosis (DVT)",
                    "value": "dx_dvt"
                },
                {
                    "label": "Pulmonary Embolus (PE)",
                    "value": "dx_pe"
                },
                {
                    "label": "Breast Cancer (Past or Present)",
                    "value": "dx_breast_cancer"
                },
                {
                    "label": "I am 35+ and smoke tobacco products",
                    "value": "dx_smoker"
                },
                {
                    "label": "A history of migraine headaches with vision changes, muscle weakness, or numbness/tingling",
                    "value": "headache"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            "validate": {
                "required": true
            },
            // "defaultValue": {"pregnant":false, "baby_recently":false, "dx_htn": false, "dx_dvt":false, "dx_pe":false, "dx_breast_cancer":false, "dx_smoker": false, "migraine_aura": false, "none":true},
            "inputType": "checkbox",
            "tableView": false,
            "customConditional": "show = _.includes(['fem_pn_ocp','fem_pn_suppress_menses'], data.sku0)",
            "optionsLabelPosition": "right"
        },
        // fem_pn_suppress_menses
        {
            "key": "ciks_fem_pn_suppress_menses",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.keys(_.pickBy(_.omit(data.ci_fem_pn_ocp, 'none')))"
        },
    ]
},
{
    "title": "Preferences",
    "type": "panel",
    "key": "page1",
    "components": [
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from prescription renewal, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                  "label": "Hair regrowth",
                  "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Prescription renewal for something else",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "validate": {"required": true},
            // "defaultValue": "none",
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "wizard-blurb",
            "type": "wizard-blurb",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "data",
            "customConditional": "show = !!data.showSubmit && !!data.final_product"
        },
        {
            "key": "faq_title",
            "html": "<h4>FAQs</h4><hr>",
            "type": "content",
            "customConditional": "show = !!data.showSubmit",
        },
        {
            "key": "faq",
            "type": "wizard-faq",
            "customConditional": "show = !!data.showSubmit",
            "rows": [
                {
                    "q": "How can I reimburse with my private insurance or Health Spending Account (HSA)?",
                    "p": "<p>Most insurance plans cover online/virtual medical consultation with a physician. We will send you a receipt for your consultation or subscription, which you can use to seek reimbursement from your private insurance or HSA.</p><p>If you require specific details just email <NAME_EMAIL> for a detailed receipt suitable for your benefits claims.</p>"
                },
                {
                    "q": "Are video visits required?",
                    "p": "<p><strong>No video visit is required;</strong> a provider will review your information 100% online. If prescribed, you’ll get online access to message your provider.</p>"
                },
                {
                    "q": "Will my current doctor find out about TeleTest? Will this affect roster status?",
                    "p": "<p><strong>No.</strong> TeleTest appointments are not OHIP-funded &amp; will not affect roster status.</p>"
                }
            ]
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

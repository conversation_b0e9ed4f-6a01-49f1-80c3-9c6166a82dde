var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Improve Your Sexual Health</h1><p class='fst-italic'>Note: currently only for men.</p>",
            "type": "content"
        },
    {
        "key": "ed_goals",
        "type": "radio",
        "input": true,
        "label": "Which best represents your situation and goals?",
        "values": [
            {
                "label": "Experiencing erectile dysfunction, want to improve performance",
                "value": "improve_performance"
            },
            {
                "label": "Occasional issues, exploring treatment options",
                "value": "exploring_options"
            },
            {
                "label": "Ready to start ED treatment ASAP",
                "value": "treatment_asap"
            },
            {
                "label": "Want to enhance performance even without issues",
                "value": "enhance_performance"
            },
            {
                "label": "Other",
                "value": "other"
            }
        ],
        "defaultValue": "improve_performance",
        "validate": { "required": true },
        "tableView": true,
        "optionsLabelPosition": "right"
    },
        {
            "key": "topic_other",
            "type": "textarea",
            "input": true,
            "label": "Please state what you're interested in:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.ed_goals=='other'"
        },
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
"mh_ed_sildenafil"  : {"n":"Generic Viagra (Sildenafil)"  ,"d":"The most trusted ED medication. Take as needed, 30-60 minutes before activity."},
"mh_ed_tadalafil"   : {"n":"Generic Cialis (Tadalafil)"   ,"d":"Longer-lasting ED medication that works up to 36 hours. Take as needed."},
"mh_ed_daily_cialis": {"n":"Daily Cialis (Tadalafil)"     ,"d":"Low-dose daily medication for spontaneous intimacy anytime."},
            }
        },
        {
            "key": "final_product",
            "type": "textfield",
            "input": true,
            "label": "Final Product:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "fp = data.sku0 ? data.sd[data.sku0] : data.sd['mh_ed_sildenafil']; value = {n:fp.n, p:39, d:fp.d, tp:'month'}"
        },
        {
            "key": "sku0",
            "type": "textfield",
            "input": true,
            "label": "SKU0:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = `mh_ed_sildenafil`;"
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = data.sku0",
            "refreshOnChange": true
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Rx Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": ""
        },
        // start ED medical questions
        {
            "key": "currently_taking_ed_meds",
            "type": "radio",
            "input": true,
            "label": "Are you currently taking any ED medications (Viagra, Cialis, Levitra, etc.)?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "defaultValue": "no",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = !!data.ed_goals",
            "optionsLabelPosition": "right"
        },
        {
            "key": "heart_conditions",
            "type": "radio",
            "input": true,
            "label": "Do you have any heart conditions or take nitrate medications?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = !!data.currently_taking_ed_meds",
            "optionsLabelPosition": "right"
        },
        {
            "key": "heart_warning",
            "html": "<p class='text-red'>ED medications can interact dangerously with heart medications, especially nitrates. Please consult with your cardiologist before starting ED treatment.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.heart_conditions=='yes'"
        },
        {
            "key": "blood_pressure_meds",
            "type": "radio",
            "input": true,
            "label": "Are you taking any blood pressure medications?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.heart_conditions=='no'",
            "optionsLabelPosition": "right"
        },
        {
            "key": "bp_note",
            "html": "<p class='text-green'>ED medications can lower blood pressure. Your physician will review your medications to ensure safe use.</p>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "customConditional": "show = data.blood_pressure_meds=='yes'"
        },
        {
            "key": "ciks_mh_ed_sub",
            "type": "textfield",
            "input": true,
            "label": "Contraindication keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.concat((data.heart_conditions=='yes' ? ['heart_condition_contraindication'] : []))",
        },
    {
        "key": "medication_preference",
        "type": "radio",
        "input": true,
        "label": "Which type of ED medication do you prefer?",
        "values": [
            {
                "label": "As-needed (take before activity)",
                "value": "as_needed"
            },
            {
                "label": "Daily (for spontaneous activity)",
                "value": "daily"
            },
            {
                "label": "No preference",
                "value": "no_preference"
            }
        ],
        "defaultValue": "as_needed",
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))",
        "optionsLabelPosition": "right"
    },
    {
        "key": "as_needed_preference",
        "type": "radio",
        "input": true,
        "label": "Which as-needed medication do you prefer?",
        "values": [
            {
                "label": "Generic Viagra (Sildenafil) - 4-6 hours",
                "value": "sildenafil"
            },
            {
                "label": "Generic Cialis (Tadalafil) - up to 36 hours",
                "value": "tadalafil"
            },
            {
                "label": "No preference",
                "value": "no_preference"
            }
        ],
        "validate": { "required": true },
        "tableView": true,
        "customConditional": "show = data.medication_preference == 'as_needed'",
        "optionsLabelPosition": "right"
    },
    {
        "key": "daily_note",
        "html": "<p class='text-green'>Daily Cialis allows for spontaneous intimacy without planning ahead. Lower dose taken daily.</p>",
        "type": "content",
        "input": true,
        "tableView": false,
        "clearOnHide": false,
        "customConditional": "show = data.medication_preference == 'daily'"
    },
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from sexual health, is there another area you'd like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Hair loss treatment",
                    "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Prescription renewal for something else",
                  "value": "rx_renewal"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "defaultValue": "none",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.idrs && _.isEmpty(_.compact(data.idrs))"
        },
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "wizard-blurb",
            "type": "wizard-blurb",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "data",
            "customConditional": "show = !!data.showSubmit && !!data.final_product"
        },
        {
            "key": "faq_title",
            "html": "<h4>FAQs</h4><hr>",
            "type": "content",
            "customConditional": "show = !!data.showSubmit",
        },
        {
            "key": "faq",
            "type": "wizard-faq",
            "customConditional": "show = !!data.showSubmit",
            "rows": [
                {
                    "q": "How can I reimburse with my private insurance or Health Spending Account (HSA)?",
                    "p": "<p>Most insurance plans cover online/virtual medical consultation with a physician. We will send you a receipt for your consultation or subscription, which you can use to seek reimbursement from your private insurance or HSA.</p><p>If you require specific details just email <NAME_EMAIL> for a detailed receipt suitable for your benefits claims.</p>"
                },
                {
                    "q": "Are video visits required?",
                    "p": "<p><strong>No video visit is required;</strong> a provider will review your information 100% online. If prescribed, you'll get online access to message your provider.</p>"
                },
                {
                    "q": "Will my current doctor find out about TeleTest? Will this affect roster status?",
                    "p": "<p><strong>No.</strong> TeleTest appointments are not OHIP-funded &amp; will not affect roster status.</p>"
                },
                {
                    "q": "How discreet is the packaging and billing?",
                    "p": "<p>All medications are shipped in discreet packaging with no indication of contents. Billing appears as 'TeleTest' on your statement.</p>"
                },
                {
                    "q": "How quickly do ED medications work?",
                    "p": "<p><strong>Sildenafil (Generic Viagra):</strong> Works in 30-60 minutes, lasts 4-6 hours.</p><p><strong>Tadalafil (Generic Cialis):</strong> Works in 30-60 minutes, lasts up to 36 hours.</p><p><strong>Daily Cialis:</strong> Builds up in your system over a few days for spontaneous activity.</p>"
                }
            ]
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

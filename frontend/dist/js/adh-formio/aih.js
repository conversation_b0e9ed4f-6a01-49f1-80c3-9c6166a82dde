var cfcs = [
{
    "title": "Plan",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "rxts",
            "type": "textfield",
            "label": "Rx Templates",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "clearOnHide": false,
            "calculateValue": [
                {
                    "products": "fem_pn_ocp,fem_pn_suppress_menses",
                    "category_choices": "womens-health,travel",
                    "key": "neta-5mg-tid-10-days",
                    "name": "NORETHINDRONE ACETATE 5MG TABLET",
                    "atc": "G03DC02",
                    "din": null,
                    "juno_drug_id": 64866,
                    "instructions": "NETA (NETA) - 5mg PO TID - Medication instruction: Start 5 days prior to the onset of menses (if known) or 5 days prior to travel date and continue for the duration of travel.  Menses will resume within 1-3 days of cessation of medication.",
                    "qty": "36",
                    "repeats": "0",
                    "long_term": false,
                    "refill_duration": 0,
                    "refill_quantity": 0,
                    "dispense_interval": 0,
                    "comment": null,
                    "fax_text": null,
                    "patient_instructions": null,
                    "count": 39,
                    "order": 10,
                    "display_name": "NETA - (10 + 2 Extra Days)"
                }
            ]
        },
        {
            "key": "insured",
            "type": "textfield",
            "label": "Insured Assays",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "clearOnHide": false,
            "calculateValue": [
                {
                    "name": "Chlamydia",
                    "active": true,
                    "key": "CT",
                    "providers": "alpha_labs, Dynacare, LifeLabs",
                    "category": "std",
                    "test_type": "urine",
                    "fu_tier": 50
                },
                {
                    "name": "Gonorrhea",
                    "active": true,
                    "key": "GC",
                    "providers": "alpha_labs, Dynacare, LifeLabs",
                    "category": "std",
                    "test_type": "urine",
                    "fu_tier": 50
                }
            ]
        },
        {
            "key": "uninsured",
            "type": "textfield",
            "label": "Insured Assays",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "clearOnHide": false,
            "calculateValue": [
            ]
        },
        {
            "key": "start_note",
            "html": "<h1>Plan</h1><p>Based on your intake responses the doctor will most likely proceed with the following plan:</p>{% if (!_.isEmpty(data.insured)||!_.isEmpty(data.uninsured)) { %}<p class='mb-0'>Lab testing for the following:</p>{% if (!_.isEmpty(data.insured)&&!_.isEmpty(data.uninsured)) { %}<strong class='mb-0'>Insured</strong>{% } %}<ul> {% data.insured.forEach(a=>{ %}<li>{{a['name']}} ({{a['test_type']}} test)</li>{% }) %}</ul>{% if (!_.isEmpty(_.compact(data.uninsured))) { %}<strong class='mb-0'>Uninsured</strong><ul>{% data.uninsured.forEach(a=>{ %}<li>{{a['name']}} ({{a['test_type']}} test)</li>{% }) %}</ul>{% } %}{% if (data.recommend_any) { %}<p class='mb-0'>The following tests were recommended to be added to your requisition:</p><ul>{% if (data.recommend_vswb) { %}<li>Vaginal self-swab<ul><li>Bacterial vaginosis (BV)</li><li>Yeast infection</li></ul></li>{% } %}{% if (data.recommend_uti) { %}<li>Urine culture<ul><li>Urinary tract-infection (UTI)</li></ul></li>{% } %}</ul>{% } %}{% } %}{% if (!_.isEmpty(data.rxts)) { %}<p>Prescription for the following:</p><ul>{% data.rxts.forEach((rx,i)=>{ %}<li>{{rx['display_name']}}</li>{% }) %}</ul>{% } %}{% data.rxts.forEach((rx,i)=>{ console.log(rx) }) %}",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "data",
            "type": "content"

        }
    ]
}
];

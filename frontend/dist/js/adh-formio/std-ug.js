var cfcs = [
{
    "title": "Goals",
    "type": "panel",
    "key": "page0",
    "components": [
        {
            "key": "start_note",
            "html": "<h1>Test & Treat STDs, UTIs, BV</h1><p>The following questionnaire will:<ol><li>Provide a recommendation based on 3-5 quick questions</li><li>Allow you to choose specific tests you may want</li><li>Determine total cost</li></ol>Note: insurance/OHIP is not required, however it can reduce out-of-pocket costs.</p>",
            "type": "content"
        },
        {
            "key": "ci_std_ug",
            "type": "selectboxes",
            "input": true,
            "label": "Are you currently experiencing any of the following symptoms?",
            "values": [
                {
                    "label": "Abdominal/pelvic pain or cramping, back pain, or rectal pain",
                    "value": "abdominal_pain_back_pain_or_rectal_pain"
                },
                {
                    "label": "Blood in my urine",
                    "value": "blood_in_urine"
                },
                {
                    "label": "Fever or feeling unwell",
                    "value": "fever_or_feeling_unwell"
                },
                {
                    "label": "Unable to urinate",
                    "value": "unable_to_urinate"
                },
                {
                    "label": "Genital rash",
                    "value": "genital_rash"
                },
                {
                    "label": "None of the above",
                    "value": "none"
                }
            ],
            // "defaultValue": {"abdominal_pain_back_pain_or_rectal_pain":true,"blood_in_urine":true,"fever_or_feeling_unwell":true,"unable_to_urinate":true,"genital_rash":true},
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "optionsLabelPosition": "right"
        },
        {
            "key": "ic",
            "type": "textfield",
            "input": true,
            "label": "Incomplete Key:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = !_.some(data.ci_std_ug) ? 'ci_std_ug' : !data.sex ? 'sex' : !_.some(data.interested_in) ? 'interested_in' : data.recommended_keys && !_.isEmpty(_.compact(data.recommended_keys)) && !data.use_recommended_tests ? 'use_recommended' : data.use_recommended_tests && !(data.use_recommended_tests == 'all'||data.all_tests_found) ? 'all_tests_found' : data.use_recommended_tests && !data.desired_test_period ? 'desired_test_period' : !data.use_insurance ? 'use_insurance' : null"
        },
        {
            "key": "idrs",
            "type": "textfield",
            "input": true,
            "label": "Intake Denial Reasons:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "ks = _.compact(_.keys(_.pickBy(_.omit(data.ci_std_ug, 'none')))); value = !_.isEmpty(ks) ? (_.some(['ci','ni','ic'], x=>_.includes(ks,x)) ? ks :_.concat('ci', ks)) : data.ic ? ['ic', data.ic] : []"
        },
        {
            "key": "red_flags_html",
            "html": "<div class='mt-3 text-red'><h3 class='text-red'>We’re unable to accept you as a patient at this time.</h3><p>Your symptoms may indicate a more complicated or non-localized infection which is inappropriate for virtual care.</p>Specifically:<ul>{{data.ci_std_ug.abdominal_pain_back_pain_or_rectal_pain?`<li><strong>Pelvic/abdominal/back/rectal pain:</strong> May indicate that an infection (such as a UTI or STD) is spreading to involve deeper pelvic structures, possibly leading to conditions like pelvic inflammatory disease (PID) or kidney involvement.</li>`:``}}{{data.ci_std_ug.blood_in_urine?`<li><strong>Blood in urine:</strong> Can be a sign of significant inflammation or damage in the urinary tract, sometimes seen with severe UTIs or complications from an STD.</li>`:``}}{{data.ci_std_ug.fever_or_feeling_unwell?`<li><strong>Fever or feeling unwell:</strong> May indicate that your body is fighting an infection that may have spread beyond a localized area.</li>`:``}}{{data.ci_std_ug.unable_to_urinate?`<li><strong>Unable to urinate:</strong> May indicate an obstruction or severe inflammation causing urinary retention, which is potentially serious.</li>`:``}}{{data.ci_std_ug.genital_rash?`<li><strong>Genital rash:</strong> May indicate infections such as herpes or syphilis.</li>`:``}}</ul><p>For proper diagnosis, an in-person exam is required. Certain treatments such as intravenous (IV) antibiotics may also require you to be in-person.</p><p>If not treated or if treatment is delayed, serious complications such as infertility and lifelong chronic pelvic pain may develop.</p></div>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = _.some(_.omit(data.ci_std_ug, 'none'))"
        },
        {
            "key": "sex",
            "type": "radio",
            "input": true,
            "label": "What was your sex assigned at birth?",
            "inline": true,
            "values": [
                {
                    "label": "Female",
                    "value": "female"
                },
                {
                    "label": "Male",
                    "value": "male"
                }
            ],
            // "defaultValue": "female",
            "validate": {
                "required": true
            },
            "tableView": false,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none'))",
            "optionsLabelPosition": "right"
        },
        {
            "key": "interested_in",
            "type": "selectboxes",
            "input": true,
            "label": "What are you interested in? (Please check all that apply)",
            "inline": false,
            "clearOnHide": false,
            "confirm_label": "What are you interested in?",
            "errorLabel": "What are you interested in?",
            "values": [
                {
                    "label": "STD testing",
                    "value": "std_testing"
                },
                {
                  "label": "STD treatment (i.e. prescription medication)",
                  "value": "std_treatment"
                },
                {
                  "label": "Urinary tract infection (UTI) testing",
                  "value": "uti_testing"
                },
                {
                  "label": "Urinary tract infection (UTI) treatment",
                  "value": "uti_treatment"
                },
                {
                  "label": "Vaginal swab test to detect bacterial vaginosis (BV) and/or yeast infection",
                  "value": "vswb_testing",
                  "customConditional": "show = data.sex=='female'"
                },
                {
                  "label": "Treatment for bacterial vaginosis (BV) and/or yeast infection",
                  "value": "vswb_treatment",
                  "customConditional": "show = data.sex=='female'"
                },
                {
                  "label": "Rectal & throat swabs to detect anal & oral chlamydia/gonorrhea",
                  "value": "rpswb"
                },
                {
                    "label": "Other",
                    "value": "other"
                }
            ],
            // "defaultValue": {"uti_testing": true},
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && !!data.sex",
        },
        {
            "key": "interested_in_other",
            "type": "textarea",
            "input": true,
            "label": "Please state your reason for selecting “other”:",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && _.get(data, 'interested_in.other')",
        },
        {
            "key": "uti_bv_symptoms_note",
            "html": "<div class='mt-3 text-orange'>Please note: if you <strong>have no symptoms</strong> the following is <strong>not</strong> medically indicated:<ul>{{data.interested_in.uti_testing?`<li><strong>UTI testing</strong></li>`:``}}{{data.interested_in.uti_treatment?`<li><strong>UTI treatment</strong></li>`:``}}{{data.interested_in.vswb_testing?`<li><strong>Vaginal swab test</strong></li>`:``}}{{data.interested_in.vswb_treatment?`<li><strong>BV and/or yeast infection treatment</strong></li>`:``}}</ul><p>By continuing you confirm you have symptoms.</p><p>Some example symptoms: pain/discomfort/burning while urinating, vaginal/penile discharge, itching, odour (not a complete list).</p></div>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = data.interested_in && (data.interested_in.uti_testing||data.interested_in.uti_treatment||data.interested_in.vswb_testing||data.interested_in.vswb_treatment)"
        },
        {
            "key": "express_shipping",
            "type": "radio",
            "input": true,
            "label": "<p>Because labs do not carry rectal & throat swabs, we ship a swab kit.</p>Shipping times & prices:<ul><li>Standard shipping: 3-7 days ($35)</li><li>Express shipping: 2-3 days ($49)</li></ul>Do you want express shipping?",
            "confirm_label": "Do you want express shipping?",
            "errorLabel": "Do you want express shipping?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            // "defaultValue": "no",
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && _.get(data, 'interested_in.rpswb')",
        },
        {
            "key": "rpswb_addon",
            "type": "radio",
            "input": true,
            "label": "Do you currently have an active TeleTest subscription?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            // "defaultValue": "no",
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && _.get(data, 'interested_in.rpswb') && !!data.express_shipping",
        }
    ]
},
{
    "title": "Plan",
    "type": "panel",
    "key": "page1",
    "components": [
        {
            "key": "recommended_area",
            "type": "textfield",
            "input": true,
            "label": "Recommended Area:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = !_.some(data.interested_in) ? null : data.interested_in.rpswb ? 'rpswb' : (data.interested_in.std_testing||data.interested_in.std_treatment) ? 'std' : (data.interested_in.vswb_testing||data.interested_in.vswb_treatment) ? 'vswb' : (data.interested_in.uti_testing||data.interested_in.uti_treatment) ? 'uti' : 'std'",
            "refreshOnChange": true
        },
        {
            "key": "td",
            "type": "textfield",
            "input": true,
            "label": "Test Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
                "CT"       : {"i":true, "p": 16, "n":"Chlamydia"},
                "GC"       : {"i":true, "p": 15, "n":"Gonorrhea"},
                "TRICH"    : {"i":true, "p":  2, "n":"Trichomoniasis"},
                "HIV"      : {"i":true, "p": 30, "n":"HIV"},
                "VDRL"     : {"i":true, "p": 18, "n":"Syphilis"},
                "UTI"      : {"i":true, "p": 15, "n":"Urine Culture (UTI Test)"},
                "VSWB"     : {"i":true, "p": 15, "n":"Vaginal Swab"},
                "RPSWB"    : {"i":true, "p": 16, "n":"Anal & Oral Swab"},
            }
        },
        {
            "key": "pd",
            "type": "textfield",
            "input": true,
            "label": "Product Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
                "vswb":{"n":"Vaginal Swab", "d":"Messaging consultation about testing & treatment for bacterial vaginosis (BV) and/or yeast infection with optional self-swab."},
                "uti" :{"n":"UTI",          "d":"Messaging consultation about UTI testing & treatment."},
                "std" :{"n":"STD",          "d":"Messaging consultation about STD testing & treatment."},
                "rpswb":{"n":"STD + Swab",  "d":"Messaging consultation about STD testing & treatment, including a swab kit for site-specific testing."},
            }
        },
        {
            "key": "sd",
            "type": "textfield",
            "input": true,
            "label": "SKU Dict:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": false,
            "clearOnHide": false,
            "calculateValue": {
                "fem_pn_vswb": {"p": 49,"tp":"once"},
                "std_pn_uti" : {"p": 49,"tp":"once"},
                "std_pn_cgtvh":{"p": 49,"tp":"once"},
                "std_sub": {"p": 59,"tp":"month"},  // Consolidated subscription product
                "std_pn_rpswb":{"p": 69,"tp":"once"},
                "std_pn_rpswb_faster":{"p": 89,"tp":"once"},
                "rpswb_addon":{"p": 35,"tp":"once"},
                "rpswb_addon_faster":{"p": 49,"tp":"once"},
            }
        },
        {
            "key": "recommended_keys",
            "type": "textfield",
            "input": true,
            "label": "Recommended keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = _.concat((data.interested_in.std_testing?_.concat(['CT','GC','HIV','VDRL'],(data.sex=='female'?['TRICH']:[])):[]),(data.interested_in.uti_testing?['UTI']:[]),(data.interested_in.vswb_testing?['VSWB']:[]),(data.interested_in.rpswb?['RPSWB']:[]))",
            "refreshOnChange": true
        },
        {
            "key": "recommended_keys_html",
            "html": "Based on what you have told us above, our recommended tests are:<ul>{% data.recommended_keys.forEach(function(k, i) { %}<li>{{_.get(data.td, k+`.n`, k)}}</li>{% }) %}</ul>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "redrawOn": "recommended_keys",
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && data.recommended_keys && !_.isEmpty(_.compact(data.recommended_keys))",
        },
        // {
        //     "key": "use_recommended_tests",
        //     "type": "radio",
        //     "input": true,
        //     "label": "Given the test recommendations, what is your desired approach?",
        //     "confirm_label": "Approach to recommended tests",
        //     "errorLabel":    "Approach to recommended tests",
        //     "values": [
        //         {
        //             "label": "I want to choose <strong>all</strong> of my own tests",
        //             "value": "none"
        //         },
        //         {
        //             "label": "I want to add <strong>additional</strong> tests to the recommended ones",
        //             "value": "some"
        //         },
        //         {
        //             "label": "I want to go with the doctor recommended tests",
        //             "value": "all"
        //         }
        //     ],
        //     // "defaultValue": "all",
        //     "validate": { "required": true },
        //     "tableView": true,
        //     "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && data.recommended_keys && !_.isEmpty(_.compact(data.recommended_keys))",
        // },
        {
            "key": "use_recommended_tests",
            "type": "textfield",
            "input": true,
            "label": "Use recommended tests:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = 'all';"
        },
        {
            "key": "specified_tests_note",
            "html": "Please specify {% if(data.use_recommended_tests=='none'){ %}<strong>all</strong>{% } else if(data.use_recommended_tests=='some'){ %}<strong>additional</strong>{% } %} desired tests by using the field below.",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && data.use_recommended_tests && data.use_recommended_tests != 'all'",
        },
        {
            "type": "select",
            "label": "Specified tests:",
            "key": "specified_tests",
            "data": {
                "custom": "values = _.map(_.pickBy(data.td,(v,k)=>(data.use_recommended_tests=='none'||!data.recommended_keys.includes(k))),(v,k)=>({value:k,label:_.get(v,`n`,k)}))"
            },
            "dataSrc": "custom",
            "multiple": true,
            "input": true,
            "tableView": true,
            "clearOnHide": false,
            "refreshOnChange": true,
            "errors": {
                "custom": "Tests must be selected if specifying all of your tests."
            },
            "validate": {
                "custom": "valid = data.use_recommended_tests != 'none' || !_.isEmpty(data.specified_tests)"
            },
            "customConditional": "show = data.use_recommended_tests && data.use_recommended_tests != 'all'"
        },
        {
          "key": "all_keys",
          "type": "textfield",
          "input": true,
          "label": "All keys:",
          "hidden": true,
          "disabled": true,
          "multiple": true,
          "tableView": true,
          "clearOnHide": false,
          "defaultValue": [],
          "calculateValue": "value = !data.use_recommended_tests ? [] : _.concat((data.use_recommended_tests=='none'?[]:data.recommended_keys), (data.use_recommended_tests=='all'?[]:_.map(data.specified_tests,(v,k)=>(v.value)).filter(k=>(data.use_recommended_tests=='none'||!data.recommended_keys.includes(k)))))",
          "refreshOnChange": true
        },
        {
            "key": "all_tests_found",
            "type": "radio",
            "input": true,
            "label": "Were you able to find all of the tests you were looking for?",
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                    "label": "No",
                    "value": "no"
                }
            ],
            // "defaultValue": "yes",
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = data.use_recommended_tests && data.use_recommended_tests != 'all'"
        },
        {
            "key": "specified_tests_other",
            "type": "textarea",
            "input": true,
            "label": "Which tests were you unable to locate?",
            "tableView": true,
            "autoExpand": false,
            "customConditional": "show = data.all_tests_found == 'no'"
        },
        {
            "key": "desired_test_period",
            "type": "radio",
            "input": true,
            "label": "How often are you interested in testing?",
            "values": [
                {
                    "label": "Not sure or based on doctor’s recommendation",
                    "value": "not_sure"
                },
                {
                    "label": "More frequently than every 3 months",
                    "value": "month"
                },
                {
                    "label": "Every 3 months",
                    "value": "3months"
                },
                {
                    "label": "Every 6 months",
                    "value": "6months"
                },
                {
                    "label": "Yearly",
                    "value": "year"
                },
                {
                    "label": "Once",
                    "value": "once"
                }
            ],
            // "defaultValue": "once",
            "validate": { "required": true },
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && data.use_recommended_tests && (data.use_recommended_tests == 'all' || data.all_tests_found)",
        },
        {
            "key": "recommended_sku",
            "type": "textfield",
            "input": true,
            "label": "Recommended SKU:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "let area2sku = {std:'std_pn_cgtvh',uti:'std_pn_uti',vswb:'fem_pn_vswb',rpswb:(_.get({nono:'std_pn_rpswb',noyes:'std_pn_rpswb_faster',yesno:'rpswb_addon',yesyes:'rpswb_addon_faster'},data.rpswb_addon+data.express_shipping))}; value = _.get(area2sku, data.recommended_area)",
            "refreshOnChange": true
        },
        {
            "key": "sub_period",
            "type": "textfield",
            "input": true,
            "label": "Subscription Period:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "tableView": true,
            "clearOnHide": false,
            "calculateValue": "value = (!['std_pn_cgtvh','std_pn_uti','fem_pn_vswb'].includes(data.recommended_sku) || !['month','3months','6months'].includes(data.desired_test_period)) ? 'once' : data.desired_test_period",
            "refreshOnChange": true
        },
        {
            "key": "use_insurance",
            "type": "radio",
            "input": true,
            "label": "Do you have health insurance (e.g. OHIP) and wish to use it?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": "yes"
                },
                {
                  "label": "No",
                  "value": "no"
                }
            ],
            // "defaultValue": "no",
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && !!data.desired_test_period",
        }
    ]
},
{
    "title": "Result",
    "type": "panel",
    "key": "page2",
    "components": [
        {
            "key": "add_areas",
            "type": "selectboxes",
            "input": true,
            "label": "Aside from lab testing, is there another area of your health you’d like to improve? (Please select all that apply.)",
            "inline": false,
            "values": [
                {
                    "label": "Other lab testing or blood work",
                    "value": "lab"
                },
                {
                    "label": "Better sex",
                    "value": "sex"
                },
                {
                  "label": "Hair regrowth",
                  "value": "hair"
                },
                {
                    "label": "Better skin",
                    "value": "skin"
                },
                {
                  "label": "Weight loss",
                  "value": "wl"
                },
                {
                  "label": "Mental health",
                  "value": "mental_health"
                },
                {
                  "label": "Prescription renewal",
                  "value": "rx_renewal"
                },
                {
                  "label": "Testosterone support",
                  "value": "testosterone",
                  "customConditional": "show = data.sex=='male'"
                },
                {
                  "label": "Birth control or cycle management",
                  "value": "birth_control",
                  "customConditional": "show = data.sex=='female'"
                },
                {
                  "label": "None of these",
                  "value": "none"
                }
            ],
            "validate": {"required": true},
            "tableView": true,
            "customConditional": "show = data.showSubmit && _.get(data, 'ci_std_ug.none') && !_.some(_.omit(data.ci_std_ug, 'none')) && data.use_insurance",
        },
        {
            "key": "display_rejected_html",
            "html": "<h3 class='text-red'>You're ineligible for monitoring at this time. Please seek in-person care with a physician to discuss your concerns.</h3>",
            "type": "content",
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "customConditional": "show = !data.showSubmit"
        },
        {
            "key": "ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = (!data.all_keys||_.isEmpty(data.all_keys)||data.use_insurance=='no') ? [] : _.filter(data.all_keys, k=>_.get(data.td,k+`.i`))",
            "refreshOnChange": true
        },
        {
            "key": "non_ohip_keys",
            "type": "textfield",
            "input": true,
            "label": "Non-OHIP keys:",
            "hidden": true,
            "disabled": true,
            "multiple": true,
            "tableView": true,
            "clearOnHide": false,
            "defaultValue": [],
            "calculateValue": "value = (!data.all_keys||_.isEmpty(data.all_keys)||!data.ohip_keys) ? [] : _.filter(data.all_keys, k=>!data.ohip_keys.includes(k))",
            "refreshOnChange": true
        },
        {
            "key": "showSubmit",
            "html": "<p class='mt-5'>To continue please click “Submit Form”</p>",
            "type": "content",
            "disabled": true,
            "input": true,
            "tableView": false,
            "clearOnHide": false,
            "refreshOnChange": true,
            "calculateValue": "value = data.idrs && _.isEmpty(_.compact(data.idrs))",
            "customConditional": "show = data.showSubmit",
        }
    ]
}
];

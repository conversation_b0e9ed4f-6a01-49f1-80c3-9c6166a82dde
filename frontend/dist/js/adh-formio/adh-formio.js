const Component = Formio.Components.components.component;
class FAQ extends Component {
    static schema(...extend) {
        return Component.schema({
            type: 'wizard-faq',
            key: 'wizard-faq',
            title: 'FAQ',
            rows: null,
        }, ...extend);
    }
    get defaultSchema() {
        return FAQ.schema();
    }
    get templateName() {
        return 'wizard-faq';
    }
    render() {
        return super.render(this.renderTemplate('wizard-faq', {
            rows: this.component.rows
        }));
    }
}
Formio.Components.addComponent('wizard-faq', FAQ);
class Blurb extends Component {
    static schema(...extend) {
        return Component.schema({
            type: 'wizard-blurb',
            key: 'wizard-blurb',
            title: 'Blurb',
            rows: null,
        }, ...extend);
    }
    get defaultSchema() {
        return Blurb.schema();
    }
    get templateName() {
        return 'wizard-blurb';
    }
    render() {
        console.log('render blurb');
        return super.render(this.renderTemplate('wizard-blurb'));
    }
}

// see: formio.js/src/components/index.js
// from: formio.js/src/components/selectboxes/SelectBoxes.js
const SelectBoxesComponent = Formio.Components.components.selectboxes;
class MySelectBoxesComponent extends SelectBoxesComponent {
    static schema(...extend) {
        return Field.schema({
            type: 'selectboxes',
            label: 'Select Boxes',
            key: 'selectBoxes',
            inline: false,
            values: [{ label: '', value: '', visible: true }],
            validate: {
                onlyAvailableItems: false
            },
            fieldSet: false
        }, ...extend);
    }
    setValuesVisibility() {
        let visibilityChanged = false;
        this.component.values.forEach((value) => {
            let visible = true;
            if (value.hasOwnProperty('conditional') || value.hasOwnProperty('customConditional')) {
                visible = FormioUtils.checkCondition(
                    value,
                    this.data,
                    this.rootValue,
                    this.root ? this.root._form : {},
                    this
                );
            }

            if (visibilityChanged && !visible) {
                let newValue;
                if (value.clearOnHide) {
                    newValue = false;
                } else if (_.has(this.component.defaultValue, value.value)) {
                    newValue = this.component.defaultValue[value.value];
                }

                if (newValue !== undefined) {
                    _.each(this.refs.input, (input) => {
                        if (input.value == value.value) {
                            this.dataValue[input.value] = newValue;
                        }

                        input.checked = !!this.dataValue[input.value];
                    });
                }
            }

            visibilityChanged = value.visible == visible;
            value.visible = visible;
        });

        return visibilityChanged;
    }
    conditionallyVisible(data, row) {
        data = data || this.rootValue;
        row = row || this.data;
        if (this.builderMode || this.previewMode || !this.hasCondition()) {
            return !this.component.hidden;
        }
        data = data || (this.root ? this.root.data : {});

        // adriang: check selectboxes values' visibilty
        const visibilityChanged = this.setValuesVisibility();
        if (visibilityChanged) {
            this.redraw();
        }

        return this.checkCondition(row, data);
    }
    hasCondition() {
        if (this._hasCondition !== null) {
            return this._hasCondition;
        }

        // adriang: check if values have a condition so listeners are attached
        let valueCondition = false;
        this.component.values.forEach((value) => {
            if (FormioUtils.hasCondition(value)) {
                valueCondition = true;
            }
        });

        if (valueCondition) {
            this._hasCondition = true;
            return this._hasCondition;
        }

        this._hasCondition = FormioUtils.hasCondition(this.component);
        return this._hasCondition;
    }

    constructor(component, options, data) {
        super(component, options, data);
        this.previousValue = this.dataValue || null;
        this.setValuesVisibility();
    }
}
Formio.Components.addComponent('wizard-blurb', Blurb);
Formio.Components.addComponent('wizard-blurb', Blurb);
Formio.Components.addComponent('selectboxes', MySelectBoxesComponent);

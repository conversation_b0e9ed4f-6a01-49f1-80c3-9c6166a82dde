<section class="{{self.bg}} {{self.padding}}">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-4 text-start text-dark py-5">
                <h1 class="landing-section-title">{{self.title}}</h1>
                <h3>{{self.subtitle}}</h3>
            </div>
            <div class="col-lg-8 text-start text-gray-700">
                <div class="accordion" id="accordionFaq">
                    <!-- {% for item in self.items %}-->
                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                        <h2 class="accordion-header" id="faqH{{forloop.counter0}}"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC{{forloop.counter0}}" aria-expanded="false" aria-controls="faqC{{forloop.counter0}}">{{item.question}}</button></h2>
                        <div class="accordion-collapse collapse" id="faqC{{forloop.counter0}}" aria-labelledby="faqH{{forloop.counter0}}" data-bs-parent="#accordionFaq"><div class="accordion-body">{{item.answer}}</div></div>
                    </div>
                    <!-- {% endfor %}-->
                </div>
            </div>
        </div>
    </div>
</section>

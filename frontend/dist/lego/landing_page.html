<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="{{page.search_description}}" />
        <meta name="author" content="" />
        {% load template_extras %}
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
        <!-- {% load wagtailcore_tags wagtailimages_tags %}-->
        <title>{{page.page_title}} - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body>
        <div id="layoutDefault">
            <div id="layoutDefault_content">
                <main>
                    <!-- Navbar-->
                    <nav class="navbar navbar-marketing navbar-expand-lg bg-white navbar-light" id="navbar">
                        <div class="container">
                            <a class="navbar-brand logo-text text-dark" href="/">
                                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                                <span class="ms-3">TeleTest.ca</span>
                                <sup><img class="img-fluid" src="/assets/img/maple_leaf.svg" /></sup>
                            </a>
                            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation"><i data-feather="menu"></i></button>
                            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                                <div class="navbar-nav ms-auto me-lg-5">
                                    <a class="nav-link" href="/app/contact/">
                                        <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                        Contact Us
                                    </a>
                                    <a class="nav-link" href="/app/about/">
                                        <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                        About Us
                                    </a>
                                    <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                        <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                        FAQ
                                    </a>
                                    <a class="nav-link" href="/blog/">
                                        <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                        Blog
                                    </a>
                                </div>
                                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                            </div>
                        </div>
                    </nav>
                    <!-- {% if product %}-->
                    <form method="POST" action="{% url 'care-view' category_key=product.category %}">
                        {% csrf_token %}
                        <!-- {% endif %}-->
                        <header class="page-header-landing page-header-light bg-white">
                            <div class="page-header-content pt-lg-0 pt-4">
                                <div class="container">
                                    <div class="row align-items-center">
                                        <div class="col-xl-6 col-lg-5">
                                            <h1 class="landing-header-title">{{page.title}}</h1>
                                            <p class="page-header-text">{{page.introduction}}</p>
                                            <p class="small fst-italic">TeleTest is currently only available in Ontario, Canada.</p>
                                            <!-- {% if product %}-->
                                            <button class="btn btn-red btn-red-shadow rounded-pill px-4 mb-5 text-lg" type="submit" name="products" value="{{product.id}}">{{page.cta_text}}</button>
                                            <!-- {% else %}-->
                                            <a class="btn btn-red btn-red-shadow rounded-pill px-4 mb-5 text-lg" href="{{page.order_href}}">{{page.cta_text}}</a>
                                            <!-- {% endif %}-->
                                        </div>
                                        <div class="col-xl-6 col-lg-7 d-none d-lg-block" data-aos="fade-up" data-aos-delay="100"><img class="img-fluid" src="{{page.img_src}}" style="{{page.img_style}}" /></div>
                                    </div>
                                </div>
                                <hr class="hr-landing" style="position: relative; z-index: 999" />
                            </div>
                        </header>
                        <!-- {% for block in page.body %}-->
                        <!-- {% if block.block_type == 'heading' %}-->
                        <h1>{{ block.value }}</h1>
                        <!-- {% else %}-->
                        {% include_block block %}
                        <!-- {% endif %}-->
                        <!-- {% endfor %}-->
                        <hr class="m-0 p-0" />
                        <section class="bg-light py-10 text-dark">
                            <div class="container">
                                <div class="row justify-content-center">
                                    <div class="col-lg-10 text-center">
                                        <div class="max-w484"><h1 class="landing-header-title">{{page.footer_title}}</h1></div>
                                        <!-- {% if product %}-->
                                        <button class="btn btn-red btn-red-shadow rounded-pill px-4 mb-5 text-lg" type="submit" name="products" value="{{product.id}}">{{page.cta_text}}</button>
                                        <!-- {% else %}-->
                                        <a class="btn btn-red btn-red-shadow rounded-pill px-4 mb-5 text-lg" href="{{page.order_href}}">{{page.cta_text}}</a>
                                        <!-- {% endif %}-->
                                    </div>
                                </div>
                            </div>
                        </section>
                        <!-- {% if product %}-->
                    </form>
                    <!-- {% endif %}-->
                </main>
            </div>
            <div id="layoutDefault_footer">
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container">
                        <div class="row mt-0">
                            <div class="col-md-12">
                                <div class="logo-listing-wrp border-top">
                                    <div class="d-flex flex-wrap align-items-center mt-4 justify-content-center">
                                        <a href="https://www.legitscript.com/websites/?checker_keywords=teletest.ca" target="_blank" title="Verify LegitScript Approval" rel="nofollow"><img src="https://static.legitscript.com/seals/10847275.png" alt="LegitScript approved" width="120" border="0" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center text-md-start text-center mt-3 mt-md-4">
                            <div class="col-md-6 small text-dark text-md-end">
                                Copyright &copy; TeleTest
                                <script>
                                    document.write(new Date().getFullYear());
                                </script>
                            </div>
                            <div class="col-md-6 small">
                                <a href="/privacy-policy.html">Privacy Policy</a>
                                &middot;
                                <a href="/terms-of-service.html">Terms of Service</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script>
            var uuid = readFromStorage('uuid');
            $.ajax({
                type: 'POST',
                url: `/app/log-row/`,
                contentType: 'application/json',
                dataType: 'json',
                data: JSON.stringify({
                    uuid: uuid,
                    href: window.location.href,
                    HTTP_REFERER: document.referrer,
                }),
                success: function (data) {
                    if (data['uuid']) {
                        uuid = data['uuid'];
                        writeToStorage('uuid', uuid);
                    }
                    if (data['gclid']) {
                        writeToStorage('gclid', data['gclid']);
                    }
                },
            });
        </script>
        <script src="/js/scripts.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
        <script>
            $('.carouselSwiper').each(function (index, element) {
                var $sw = $(element);
                var sw2 = new Swiper($sw[0], {
                    centeredSlides: true,
                    grabCursor: true,
                    loop: true,
                    pagination: {
                        //- el: '.' + paginationClass,
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    breakpoints: {
                        640: {
                            slidesPerView: 1,
                            spaceBetween: 30,
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30,
                        },
                    },
                });
            });

            var swiper2 = new Swiper('.reviewSwiper', {
                centeredSlides: true,
                grabCursor: true,
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    640: {
                        slidesPerView: 1,
                        spaceBetween: 30,
                    },
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30,
                    },
                },
            });

            let swiper = new Swiper('.my-swiper', {
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });

            let responsiveSwiper = new Swiper('.responsive-swiper', {
                loop: true,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                init: true,
                navigation: {
                    //- nextEl: ".swiper-button-next",
                    //- prevEl: ".swiper-button-prev",
                },
            });
        </script>
    </body>
</html>

<!DOCTYPE html>
<!-- {% load template_extras %}-->
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Secure Mail Conversation - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link href="/assets/demo/muuri-inbox.css" rel="stylesheet" crossorigin="anonymous" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <a class="navbar-brand logo-text pe-3 ps-1 ps-lg-3" href="/">
                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                <span class="ms-3">TeleTest.ca</span>
            </a>
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i class="feather-lg" data-feather="menu"></i></button>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto me-lg-5 me-1">
                <!-- {% if not user.is_authenticated %}-->
                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                <!-- {% else %}-->
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="/assets/img/icons/users/user-0c.svg" />
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">{{user.profile.name}}</div>
                                <div class="dropdown-user-details-email">{{user.email}}</div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/app/account-details/">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="/app/accounts/logout/">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
                <!-- {% endif %}-->
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <!-- if categories-->
                <!-- {% if logout_redirect and logout_redirect == False %}-->
                <!-- false-->
                <!-- {% else %}-->
                <!-- true-->
                {{ redirect_to_login_immediately }}
                <!-- {% endif %}-->
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <a class="nav-link" href="/app/patient-portal/">
                                <div class="nav-link-icon"><i data-feather="home"></i></div>
                                Patient Portal
                            </a>
                            <a class="nav-link" href="/app/contact/">
                                <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                Contact Us
                            </a>
                            <a class="nav-link" href="/app/about/">
                                <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                About Us
                            </a>
                            <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                FAQ
                            </a>
                            <a class="nav-link" href="/blog/">
                                <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                Blog
                            </a>
                            <a class="nav-link" href="/app/lab-testing/ON/cities/">
                                <div class="nav-link-icon"><i data-feather="clock"></i></div>
                                Lab Times
                            </a>
                            <!-- {% if user.is_authenticated %}-->
                            <a class="nav-link" href="/app/provider-info/">
                                <div class="nav-link-icon"><i class="fas fa-notes-medical"></i></div>
                                Provider Info
                            </a>
                            <!-- {% endif %}-->
                            <div class="sidenav-menu-heading">Healthcare</div>
                            <a class="nav-link" href="/app/categories/">
                                <div class="nav-link-icon"><i data-feather="layers"></i></div>
                                Categories
                            </a>
                            <!-- {% if not categories %}-->
                            <a class="nav-link" href="/app/care/std/">
                                <div class="nav-link-icon"><i class="fas fa-venus-mars"></i></div>
                                STD
                            </a>
                            <!-- {% else %} {% for category in categories %}-->
                            <a class="nav-link" href="{{category.url}}">
                                <div class="nav-link-icon"><i class="fas fa-{{category.icon}}"></i></div>
                                {{category.name}}
                            </a>
                            <!-- {% endfor %} {% endif %}-->
                            <div class="sidenav-menu-heading">Intake</div>
                            <a class="nav-link" href="/app/intake/">
                                <div class="nav-link-icon"><i class="fas fa-stethoscope"></i></div>
                                Virtual Care Assessment
                            </a>
                            <a class="nav-link" href="/app/account-details/">
                                <div class="nav-link-icon"><i data-feather="user"></i></div>
                                Account Details
                            </a>
                            <a class="nav-link" href="/app/find-location/lab/">
                                <div class="nav-link-icon"><i class="fas fa-search-location"></i></div>
                                Find Lab
                            </a>
                            <a class="nav-link" href="/app/payment/">
                                <div class="nav-link-icon"><i class="fas fa-credit-card"></i></div>
                                Checkout
                            </a>
                        </div>
                    </div>
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <!-- {% if user.is_authenticated %}-->
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title profileEmail"></div>
                            {{user.email}}
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="border-bottom bg-white mb-3">
                        <div class="container d-flex justify-content-center">
                            <div class="col-xxl-8 col">
                                <div class="row pt-3 pb-3 align-items-center">
                                    <div class="col-md-1 col-2 text-end"><a class="small" href="/app/inbox/">Back</a></div>
                                    <div class="col-md-11 col-10 text-start">
                                        <div class="d-flex flex-column line-height-normal">
                                            {{room.roomName|truncatechars:30}}
                                            <div class="small text-muted mt-1">{{room.doctorName}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="container d-flex justify-content-center">
                        <div class="col-xxl-8 mb-lg-5">
                            <div class="container text-center">
                                <!-- {% for msg in messages %}-->
                                <!-- {% if msg.senderId == currentUserId %}-->
                                <div class="row">
                                    <div class="col-11 text-end">
                                        <div class="d-flex flex-column line-height-normal text-end">
                                            <div class="text-xs">{{msg.sender.name}}</div>
                                            <div class="text-xs text-muted">{{msg.dt}}</div>
                                        </div>
                                    </div>
                                    <div class="col-1 text-start ps-0">
                                        <div class="avatar avatar-sm"><img class="avatar-img img-fluid" src="{{msg.sender.avatar}}" /></div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-1"></div>
                                    <div class="col-11 col-md-10 text-start">
                                        <div class="card small bg-blue-soft speech-bubble-end me-n3 shadow-none">
                                            <div class="card-body">
                                                {{msg.html|safe}}
                                                <!-- {% if msg.files %}-->
                                                <hr />
                                                <!-- {% for file in msg.files %}-->
                                                <div class="row mb-3"><a href="{{file.url}}" target="_blank">{{file.name}}</a></div>
                                                <!-- {% endfor %} {% endif %}-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-1"></div>
                                </div>
                                <!-- {% elif msg.system %}-->
                                <div class="row justify-content-center mb-3">
                                    <div class="col-9">
                                        <div class="card text-xs text-center shadow-none bg-white"><div class="card-body py-2">{{msg.html|safe}}</div></div>
                                    </div>
                                </div>
                                <!-- {% elif msg.senderId and msg.senderId != currentUserId %}-->
                                <div class="row">
                                    <div class="col-1 text-end pe-0">
                                        <div class="avatar avatar-sm {%if msg.sender.bot %}avatar-square{% endif %}"><img class="avatar-img img-fluid" src="{{msg.sender.avatar}}" /></div>
                                    </div>
                                    <div class="col-11 text-start">
                                        <div class="d-flex flex-column line-height-normal">
                                            <div class="text-xs">{{msg.sender.name}}</div>
                                            <div class="text-xs text-muted">{{msg.dt}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-1"></div>
                                    <div class="col-11 col-md-10 text-start">
                                        <div class="card small speech-bubble-start ms-n3 shadow-none">
                                            <div class="card-body">
                                                {{msg.html|safe}}
                                                <!-- {% if msg.files %}-->
                                                <hr />
                                                <!-- {% for file in msg.files %}-->
                                                <div class="row mb-3"><a href="{{file.url}}" target="_blank">{{file.name}}</a></div>
                                                <!-- {% endfor %} {% endif %}-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- {% endif %}-->
                                <!-- {% endfor %}-->
                            </div>
                            <!-- {% if not required %}-->
                            <form class="modal fade" id="app-form" tabindex="-1" role="dialog" method="POST">
                                {% csrf_token %}
                                <div class="modal-dialog modal-xl" role="document" style="margin-top: 10%; min-height: 60%">
                                    <div class="modal-content h-100">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="app-form-title">Continue</h5>
                                            <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <!-- {% if 'get-req' in rt_keys %}-->
                                            <p class="text-center">If your intake history is correct &amp; you want to get tested immediately click the button below to get your lab requisiton:</p>
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value="get-req">
                                                                Confirm &amp; Get Requisition
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'get-req-rx' in rt_keys %}-->
                                            <p class="text-center">If your intake history is correct click the button below to get your lab requisiton &amp; prescription:</p>
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value="get-req-rx">
                                                                Confirm &amp; Get Requisition &amp; Prescription
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'get-rx' in rt_keys %}-->
                                            <p class="text-center">If your intake history is correct click the button below to get your prescription:</p>
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value="get-rx">
                                                                Confirm &amp; Get Prescription
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'edit-intake' in rt_keys %}-->
                                            <hr />
                                            <p class="text-center">If your intake history is incorrect please click the button below:</p>
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value="edit-intake">
                                                                Edit Intake History
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'appointment-decline' in rt_keys %}-->
                                            <hr />
                                            <p class="text-center">If you would like to decline scheduling an appointment:</p>
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value="appointment-decline">
                                                                Decline Appointment
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'appointment-intake' in rt_keys or 'appointment-fu' in rt_keys %}-->
                                            <hr />
                                            <p class="text-center">Please click the button below to schedule an appointment:</p>
                                            <!-- {% if next_appt %}-->
                                            <div class="card-icon">
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <div class="card-icon-aside"><i class="me-1 text-blue-50" data-feather="calendar"></i></div>
                                                    <div>
                                                        <h5 class="card-title">Next Appointment:</h5>
                                                        <p class="card-text">{{next_appt|date:"l, F j @ g:iA T"}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <div class="w-100 px-3">
                                                <div class="row align-items-center justify-content-center">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500" type="submit" role="button" name="rt-key" value='{% if "appointment-intake" in rt_keys %}appointment-intake{% else %}appointment-fu{% endif %}'>
                                                                Schedule Appointment
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% endif %}-->
                                            <!-- {% if 'free-text' in rt_keys and user.is_doctor %}-->
                                            <div class="mb-0 h-100">
                                                <textarea class="form-control h-100" type="text" name="text" placeholder="Enter your message..." rows="10"></textarea>
                                                <div class="row align-items-center justify-content-center mt-3">
                                                    <div class="col-lg-6">
                                                        <div class="d-grid">
                                                            <button class="btn btn-primary btn-shadow shadow-lg fw-500 text-lg" type="submit" role="button" name="rt-key" value="free-text">
                                                                Send
                                                                <i class="fa fa-refresh fa-spin button-icon-right ms-2 btn-spinner" style="display: none"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- {% else %}-->
                                            <a href="{% url 'contact_form' %}">Contact Us Here</a>
                                            <!-- {% endif %}-->
                                        </div>
                                        <div class="modal-footer"><button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Close</button></div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="position-fixed position-md-static bottom-0 pb-3 w-100 px-3" style="z-index: 1030">
                        <div class="row align-items-center justify-content-center mt-3">
                            <div class="col-md-6 col-xl-4">
                                <!-- {% if not room.completed %}-->
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-shadow fw-500 text-lg" type="button" data-bs-toggle="modal" data-bs-target="#app-form">
                                        Continue
                                        <i class="feather-lg ms-2" data-feather="arrow-right"></i>
                                    </button>
                                </div>
                                <!-- {% else %}-->
                                <div class="shadow bg-white text-center fst-italic rounded border p-1 small">
                                    <div>You cannot reply to a completed conversation</div>
                                    <a href="{% url 'contact_form' %}">Contact Us Here</a>
                                </div>
                                <!-- {% endif %}-->
                            </div>
                        </div>
                    </div>
                    <!-- {% endif %}-->
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container-fluid"><div class="row"></div></div>
                </footer>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script src="/js/scripts.js"></script>
        <!-- {% if pre_req %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('requiredModal'), { backdrop: 'static', keyboard: false });
            modal.show();
            if ($('#loading-spinner').length) {
                $('#loading-spinner').hide();
            }
        </script>
        <!-- {% else %}-->
        <!-- {% if coupon_text %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('couponModal'), {});
            modal.show();
            let url = new URL(location.href);
            url.searchParams.delete('coupon');
            window.history.pushState({}, document.title, url);
        </script>
        <!-- {% endif %}-->
        <!-- {% endif %}-->
        <script>
            // Debounce form
            let disableStuff = function (t) {
                $('#app-form .btn:submit').prop('disabled', true);
                $(t).children('.btn-spinner').show();
            };

            let enableStuff = function (t) {
                $('#app-form .btn:submit').prop('disabled', false);
                $(t).children('.btn-spinner').hide();
            };

            $('#app-form').on('click', '.btn:submit', function (e) {
                e.preventDefault();
                let btn = this;
                let form = $('#app-form');
                disableStuff(btn);
                setTimeout(() => {
                    enableStuff(btn);
                }, 4000);
                if (!form[0].checkValidity()) {
                    // display native HTML5 error messages
                    form[0].reportValidity();
                } else {
                    let tempElement = $("<input type='hidden'/>");
                    tempElement.attr('name', this.name).val(this.value).appendTo(form);
                    form.submit();
                    tempElement.remove();
                }
            });
        </script>
    </body>
</html>

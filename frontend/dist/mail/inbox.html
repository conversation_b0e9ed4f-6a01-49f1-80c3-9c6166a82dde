<!DOCTYPE html>
<!-- {% load template_extras %}-->
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Inbox - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link href="/assets/demo/muuri-inbox.css" rel="stylesheet" crossorigin="anonymous" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <a class="navbar-brand logo-text pe-3 ps-1 ps-lg-3" href="/">
                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                <span class="ms-3">TeleTest.ca</span>
            </a>
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i class="feather-lg" data-feather="menu"></i></button>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto me-lg-5 me-1">
                <!-- {% if not user.is_authenticated %}-->
                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                <!-- {% else %}-->
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="/assets/img/icons/users/user-0c.svg" />
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">{{user.profile.name}}</div>
                                <div class="dropdown-user-details-email">{{user.email}}</div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/app/account-details/">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="/app/accounts/logout/">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
                <!-- {% endif %}-->
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <!-- if categories-->
                <!-- {% if logout_redirect and logout_redirect == False %}-->
                <!-- false-->
                <!-- {% else %}-->
                <!-- true-->
                {{ redirect_to_login_immediately }}
                <!-- {% endif %}-->
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <a class="nav-link" href="/app/patient-portal/">
                                <div class="nav-link-icon"><i data-feather="home"></i></div>
                                Patient Portal
                            </a>
                            <a class="nav-link" href="/app/contact/">
                                <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                Contact Us
                            </a>
                            <a class="nav-link" href="/app/about/">
                                <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                About Us
                            </a>
                            <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                FAQ
                            </a>
                            <a class="nav-link" href="/blog/">
                                <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                Blog
                            </a>
                            <a class="nav-link" href="/app/lab-testing/ON/cities/">
                                <div class="nav-link-icon"><i data-feather="clock"></i></div>
                                Lab Times
                            </a>
                            <!-- {% if user.is_authenticated %}-->
                            <a class="nav-link" href="/app/provider-info/">
                                <div class="nav-link-icon"><i class="fas fa-notes-medical"></i></div>
                                Provider Info
                            </a>
                            <!-- {% endif %}-->
                            <div class="sidenav-menu-heading">Healthcare</div>
                            <a class="nav-link" href="/app/categories/">
                                <div class="nav-link-icon"><i data-feather="layers"></i></div>
                                Categories
                            </a>
                            <!-- {% if not categories %}-->
                            <a class="nav-link" href="/app/care/std/">
                                <div class="nav-link-icon"><i class="fas fa-venus-mars"></i></div>
                                STD
                            </a>
                            <!-- {% else %} {% for category in categories %}-->
                            <a class="nav-link" href="{{category.url}}">
                                <div class="nav-link-icon"><i class="fas fa-{{category.icon}}"></i></div>
                                {{category.name}}
                            </a>
                            <!-- {% endfor %} {% endif %}-->
                            <div class="sidenav-menu-heading">Intake</div>
                            <a class="nav-link" href="/app/intake/">
                                <div class="nav-link-icon"><i class="fas fa-stethoscope"></i></div>
                                Virtual Care Assessment
                            </a>
                            <a class="nav-link" href="/app/account-details/">
                                <div class="nav-link-icon"><i data-feather="user"></i></div>
                                Account Details
                            </a>
                            <a class="nav-link" href="/app/find-location/lab/">
                                <div class="nav-link-icon"><i class="fas fa-search-location"></i></div>
                                Find Lab
                            </a>
                            <a class="nav-link" href="/app/payment/">
                                <div class="nav-link-icon"><i class="fas fa-credit-card"></i></div>
                                Checkout
                            </a>
                        </div>
                    </div>
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <!-- {% if user.is_authenticated %}-->
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title profileEmail"></div>
                            {{user.email}}
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-compact page-header-light border-bottom bg-white mb-2">
                        <div class="container-xl">
                            <div class="page-header-content">
                                <div class="row align-items-center justify-content-between pt-3">
                                    <div class="col-auto mb-3">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="mail"></i></div>
                                            Inbox
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="container">
                        <nav class="nav nav-borders">
                            <a class="nav-link ms-0" href='{% url "patient" %}'>Details</a>
                            <a class="nav-link" href='{% url "pmh" %}'>History</a>
                            <a class="nav-link" href='{% url "patient-portal" %}'>Portal</a>
                            <a class="nav-link" href='{% url "subscriptions" %}'>Subscriptions</a>
                            <a class="nav-link active" href='{% url "inbox" %}'>
                                Inbox
                                <!-- {% if unread_count %}-->
                                <div class="btn btn-icon btn-xs btn-blue">{{unread_count}}</div>
                                <!-- {% endif %}-->
                            </a>
                        </nav>
                        <hr class="mt-0 mb-3" />
                        <!-- {% if pre_req %}-->
                        <!-- pre_req == {{pre_req}}-->
                        <div class="modal fade" id="requiredModal" tabindex="-1" role="dialog" aria-labelledby="requiredModalLabel" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="text-center modal-title" id="requiredModalLabel">
                                            <!-- {% if pre_req == 'purchase' %}-->
                                            This Has Already Been Paid For
                                            <!-- {% elif pre_req == 'vca' %}-->
                                            Please Complete Intake Form
                                            <!-- {% elif pre_req == 'profile' %}-->
                                            Please Complete Account Details
                                            <!-- {% else %}-->
                                            Please Choose a Product
                                            <!-- {% endif %}-->
                                        </h5>
                                    </div>
                                    <div class="modal-body">
                                        <p class="lead">
                                            <a href="{% if pre_req == 'vca' %}{% url 'vca' %}{% elif pre_req == 'profile' %}{% url 'patient' %}{% else %}{% url 'categories' %}{% endif %}">
                                                <!-- {% if pre_req == 'purchase' %}-->
                                                If you’d another product, please get started by selecting a category.
                                                <!-- {% elif pre_req == 'vca' %}-->
                                                Intake form must be completed prior to filling out your account details.
                                                <!-- {% elif pre_req == 'profile' %}-->
                                                Account details must be completed prior to filling out your intake form.
                                                <!-- {% else %}-->
                                                Please select at least one product to continue.
                                                <!-- {% endif %}-->
                                            </a>
                                        </p>
                                        <p><a href="{% url 'contact_form' %}">Contact Us if you have any questions or if something isn't working as you expected.</a></p>
                                        <div class="row align-items-center justify-content-center">
                                            <div class="col-lg-6">
                                                <div class="d-grid">
                                                    <a class="btn btn-primary btn-lg btn-shadow" type="button" href="{% if pre_req == 'vca' %}{% url 'vca' %}{% elif pre_req == 'profile' %}{% url 'patient' %}{% else %}{% url 'categories' %}{% endif %}">
                                                        <i class="me-2" data-feather="arrow-left"></i>
                                                        <!-- {% if pre_req == 'vca' %}-->
                                                        Complete Intake
                                                        <!-- {% elif pre_req == 'profile' %}-->
                                                        Complete Account Details
                                                        <!-- {% else %}-->
                                                        Categories
                                                        <!-- {% endif %}-->
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- {% elif coupon_text %}-->
                        <div class="modal fade" id="couponModal" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="text-center modal-title" id="couponModalLabel">Coupon</h5>
                                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">{{coupon_text|safe}}</div>
                                    <div class="modal-footer"><button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Close</button></div>
                                </div>
                            </div>
                        </div>
                        <!-- {% endif %}-->
                        <!-- {% if False %}-->
                        <hr class="mt-2 my-4" />
                        <a class="card-link" href="https://teletest.ca">
                            <div class="alert alert-icon shadow alert-orange lift pointer">
                                <div class="alert-icon-aside"><i class="fas fa-triangle-exclamation"></i></div>
                                <div class="alert-icon-content">
                                    <h6 class="alert-heading">THIS IS A TEST SITE</h6>
                                    Ordering is disabled on this site, please see our main site at
                                    <a href="https://teletest.ca">TeleTest.ca</a>
                                </div>
                            </div>
                        </a>
                        <!-- {% endif %}-->
                        <form id="app-form" method="POST">
                            {% csrf_token %}
                            <section class="grid-demo">
                                <div class="container">
                                    <div class="row align-items-center justify-content-center">
                                        <div class="control search col-lg-4 col-12">
                                            <div class="control-icon"><i class="material-icons">&#xE8B6;</i></div>
                                            <input class="control-field search-field form-control" type="text" name="search" placeholder="Search..." />
                                        </div>
                                        <div class="control sort col-lg-4 col-12">
                                            <div class="control-icon"><i class="material-icons">&#xE164;</i></div>
                                            <div class="select-arrow"><i class="material-icons">&#xE313;</i></div>
                                            <select class="control-field sort-field form-control">
                                                <option value="seconds-desc" selected="">Sort by Newest</option>
                                                <option value="seconds-asc">Oldest</option>
                                                <option value="title">Title</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="div container">
                                    <div id="grid-spinner">
                                        <div class="d-flex justify-content-center"><div class="spinner-border text-primary mt-3 mb-5" role="status" aria-hidden="true" style="width: 4rem; height: 4rem"></div></div>
                                    </div>
                                    <div class="grid" style="display: none">
                                        <!-- {% if rooms %}{% for room in rooms %}-->
                                        <label class="item w-100 mt-2" data-title="{{room.roomName}}" data-category="{{room.roomName}}" data-words="{{room.words}}" data-seconds="{{room.lastUpdated.seconds}}">
                                            <a class="card lift" href="/app/thread/{{room.id}}/">
                                                <!-- {% if room.unreadCount %}-->
                                                <div class="badge-notify badge-right btn btn-icon btn-xs btn-blue">{{room.unreadCount}}</div>
                                                <!-- {% endif %}-->
                                                <div class="card-body pb-0">
                                                    <div class="d-flex align-items-center justify-content-between mb-3 {%if room.unreadCount%}fw-bold{%endif%}">
                                                        <div class="d-flex align-items-center flex-shrink-0 me-3">
                                                            <div class="avatar avatar-lg me-3 {% if room.doctorName == 'TeleBot' %}avatar-square{% else %}bg-gray-200{% endif %}"><img class="avatar-img" src="{{room.avatar}}" /></div>
                                                            <div class="d-flex flex-column">
                                                                <div class="fw-bold line-height-normal mb-1">{{room.doctorName}}</div>
                                                                <div class="small line-height-normal mb-1">{{room.roomName|truncatechars:30}}</div>
                                                                <div class="small text-muted line-height-normal">{{room.lastMessage.content|truncatechars:30}}</div>
                                                            </div>
                                                        </div>
                                                        <div class="small"><div class="text-xs text-dark">{{room.dtLastUpdated}}</div></div>
                                                    </div>
                                                </div>
                                            </a>
                                        </label>
                                        <!-- {% endfor %}{% else %}-->
                                        <label class="item w-100 mt-2" data-title="empty" data-category="empty" data-words="empty" data-seconds="0">
                                            <div class="card">
                                                <div class="card-body pb-0">
                                                    <div class="d-flex align-items-center flex-shrink-0 mb-3">
                                                        <div class="avatar avatar-lg me-3 bg-gray-200"><img class="avatar-img" src="/assets/img/logo/avatar-512w.png" /></div>
                                                        <div class="d-flex flex-column">
                                                            <div class="fw-bold line-height-normal mb-1">Welcome to Secure Mail</div>
                                                            <div class="small line-height-normal mb-1" style="flex-wrap: wrap; min-width: 0">Mail your doctor sends you will appear here.</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>
                                        <!-- {% endif %}-->
                                    </div>
                                </div>
                            </section>
                            <!-- {% if not required %}-->
                            <!-- {% endif %}-->
                        </form>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container">
                        <div class="row mt-0">
                            <div class="col-md-12">
                                <div class="logo-listing-wrp border-top">
                                    <div class="d-flex flex-wrap align-items-center mt-4 justify-content-center">
                                        <a href="https://www.legitscript.com/websites/?checker_keywords=teletest.ca" target="_blank" title="Verify LegitScript Approval" rel="nofollow"><img src="https://static.legitscript.com/seals/10847275.png" alt="LegitScript approved" width="120" border="0" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center text-md-start text-center mt-3 mt-md-4">
                            <div class="col-md-6 small text-dark text-md-end">
                                Copyright &copy; TeleTest
                                <script>
                                    document.write(new Date().getFullYear());
                                </script>
                            </div>
                            <div class="col-md-6 small">
                                <a href="/privacy-policy.html">Privacy Policy</a>
                                &middot;
                                <a href="/terms-of-service.html">Terms of Service</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script src="/js/scripts.js"></script>
        <!-- {% if pre_req %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('requiredModal'), { backdrop: 'static', keyboard: false });
            modal.show();
            if ($('#loading-spinner').length) {
                $('#loading-spinner').hide();
            }
        </script>
        <!-- {% else %}-->
        <!-- {% if coupon_text %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('couponModal'), {});
            modal.show();
            let url = new URL(location.href);
            url.searchParams.delete('coupon');
            window.history.pushState({}, document.title, url);
        </script>
        <!-- {% endif %}-->
        <!-- {% endif %}-->
        <script src="/assets/libs/muuri/velocity-1.5.0.min.js"></script>
        <script src="/assets/libs/muuri/hammer-2.0.8.min.js"></script>
        <script src="/assets/libs/muuri/muuri-0.9.5.min.js"></script>
        <script src="/assets/demo/muuri-inbox.js"></script>
        <script>
            //- stop enter from submitting form
            //- see: https://stackoverflow.com/questions/895171/prevent-users-from-submitting-a-form-by-hitting-enter
            $(document).on('keydown', ':input:not(:submit)', function (event) {
                if (event.key == 'Enter') {
                    console.log('Enter key disallowed');
                    event.preventDefault();
                }
            });
            //- $(function () { initMuuri(); });
        </script>
    </body>
</html>

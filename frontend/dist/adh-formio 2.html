<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Intake - TeleTest.ca</title>
        <link href="/css/styles.css?v=3" rel="stylesheet" />
        <link href="https://formio.github.io/formio.js/dist/formio.full.css" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body>
        <div id="layoutDefault">
            <div id="layoutDefault_content">
                <main>
                    <div class="container">
                        <div class="row justify-content-center pt-2 pb-3">
                            <div class="col-lg-10 col-xxl-9">
                                <div id="formio">
                                    <div class="d-flex justify-content-center"><div class="spinner-border text-primary mt-3 mb-5" id="loading-spinner" role="status" style="width: 4rem; height: 4rem"></div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
            <div id="layoutDefault_footer"></div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script>
            var uuid = readFromStorage('uuid');
            $.ajax({
                type: 'POST',
                url: `/app/log-row/`,
                contentType: 'application/json',
                dataType: 'json',
                data: JSON.stringify({
                    uuid: uuid,
                    href: window.location.href,
                    HTTP_REFERER: document.referrer,
                }),
                success: function (data) {
                    if (data['uuid']) {
                        uuid = data['uuid'];
                        writeToStorage('uuid', uuid);
                    }
                    if (data['gclid']) {
                        writeToStorage('gclid', data['gclid']);
                    }
                },
            });
        </script>
        <script src="/js/scripts.js"></script>
        <script src="https://formio.github.io/formio.js/dist/formio.full.js"></script>
        <script src="/assets/libs/moment.js/moment-2.29.4.min.js"></script>
        <script>
            //- window.adh_key = 'mh-hl';
            window.adh_key = '{{adh.adh_key}}';
        </script>
        <!-- {% verbatim %}-->
        <script>
            Formio.Templates.current['wizard'] = { form: `<div class="card mt-3 {{ctx.className}}"><div class="card-header border-bottom">{{ ctx.wizardHeader }}</div><div class="card-body"><div class="row justify-content-center"><div class="col-xxl-6 col-xl-8"><div class="wizard-page" ref="{{ctx.wizardKey}}">{{ctx.components}}</div>{{ ctx.wizardNav }}</div></div></div></div>` };
            Formio.Templates.current['wizardHeader'] = {
                form: `<nav class="nav nav-pills nav-justified flex-column flex-xl-row nav-wizard" aria-label="Wizard navigation" id="{{ ctx.wizardKey }}-header" ref="{{ctx.wizardKey}}-header" role="tablist"><!-- {% ctx.panels.forEach(function(panel, index) { %}--><a class="nav-item nav-link page-item{{ctx.currentPage === index ? ' active' : ''}}" style="cursor: {% if (ctx.isBreadcrumbClickable) { %}pointer{% } else { %}default{% } %};" data-index="{{index}}" role="tab" ref="{{ctx.wizardKey}}-link"><div class="wizard-step-icon"><!-- {% if (index == 0) { %}--><i class="fa-solid fa-stethoscope"></i><!-- {% } else if (index == ctx.panels.length - 1) { %}--><i class="fa-solid fa-square-poll-horizontal"></i><!-- {% } else { %}-->{{index}}<!-- {% } %}--></div><div class="wizard-step-text"><div class="wizard-step-text-name"><!-- {% if (index == 0) { %}-->General<!-- {% } else { %}-->{{panel.title}}<!-- {% } %}--></div></div></a><!-- {% }) %}--></nav>`,
            };
            Formio.Templates.current['wizardNav'] = {
                form: `<hr class="my-4"><div class="d-flex justify-content-between formio-wizard-nav-container" id="{{ ctx.wizardKey }}-nav"><!-- {% if (ctx.currentPage == 0) { %}--><button class="mb-3 mb-sm-0 btn btn-light disabled" ref="{{ctx.wizardKey}}-previous" aria-label="{{ctx.t('previous')}} button. Click to go back to the previous tab" disabled>{{ctx.t('previous')}}</button><!-- {% } else { %}--><button class="mb-3 mb-sm-0 btn btn-light btn-wizard-nav-previous" ref="{{ctx.wizardKey}}-previous" aria-label="{{ctx.t('previous')}} button. Click to go back to the previous tab">{{ctx.t('previous')}}</button><!-- {% } %}--><!-- {% if (ctx.currentPage != ctx.panels.length - 1) { %}--><button class="btn btn-primary btn-wizard-nav-next" ref="{{ctx.wizardKey}}-next" aria-label="{{ctx.t('next')}} button. Click to go to the next tab">{{ctx.t('next')}}</button><!-- {% } %}--><!-- {% ctx.buttonOrder.forEach(function(button) { %}--><!-- {% if (button === 'submit' && ctx.buttons.submit) { %}--><!-- {% if (window.kind!='pmh' && !data.showSubmit) { %}--><button class="btn btn-primary btn-wizard-nav-submit" ref="{{ctx.wizardKey}}-submit" aria-label="{{ctx.t('submit')}} button. Click to submit the form" disabled="true">{{ctx.t('submit')}}</button><!-- {% } else { %}--><button class="btn btn-primary btn-wizard-nav-submit" ref="{{ctx.wizardKey}}-submit" aria-label="{{ctx.t('submit')}} button. Click to submit the form">{{ctx.t('submit')}}</button><!-- {% } %}--><!-- {% } %}--><!-- {% }) %}--></div>`,
            };
            Formio.Templates.current['wizard-faq'] = { form: `<div class="accordion" id="accordionFaq"><!-- {% ctx.rows.forEach(function(row, i) { %}--><div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm"><h2 class="accordion-header" id="faqH{{i}}"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC{{i}}" aria-expanded="false" aria-controls="faqC{{i}}">{{row.q}}</button></h2><div class="accordion-collapse collapse" id="faqC{{i}}" aria-labelledby="faqH{{i}}" data-bs-parent="#accordionFaq"><div class="accordion-body">{{row.p}}</div></div></div><!-- {% }) %}--></div>` };
            Formio.Templates.current['wizard-blurb'] = {
                form: `<!-- {% if (!!obj.data.showSubmit && !!obj.data.final_product) { %}--><div class="card round bg-blue-soft mb-5" id="final-product"><div class="card-header"><div class="text-blue-hard"><div class="d-flex justify-content-between align-items-center"><div class="div">{{obj.data.final_product.n}}</div><div class="badge bg-green-soft rounded-pill badge-marketing badge-md text-green">$ {{obj.data.final_product.p}} /{{obj.data.final_product.tp}}</div></div></div></div><div class="card-body px-0 pt-2 bg-white"><div class="px-3 text-gray-700">{{obj.data.final_product.d}}</div>{% if (obj.data.rx) { %}<ul class="pt-3 ps-3 list-group list-group-flush"><div class="small">Medication:</div><li class="list-group-item"><i class="me-2 fas fa-prescription text-blue"></i><span class="small">{{obj.data.rx.label}}</span></li></ul>{% } %}
{% if (obj.data.ohip_keys && !_.isEmpty(_.compact(obj.data.ohip_keys))) { %}<ul class="pt-3 ps-2 list-group list-group-flush">Standard tests{% if (obj.data.use_insurance=='yes') { %} (insured){% } %}:
{% obj.data.ohip_keys.forEach(function(k, i) { %}<li class="list-group-item"><i class="me-2 fas fa-syringe text-blue"></i><span class="small">{{_.get(obj.data.td, k+'.n', k)}}{% if (obj.data.use_insurance=='no') { %}: \${{_.get(obj.data.td, k+'.p')}}{% } %}</span></li>{% }) %}<em class="text-xs">{% if (obj.data.use_insurance=='yes') { %}No additional fee when medically indicated{% } %}</em></ul>{% } %}
{% if (obj.data.non_ohip_keys && !_.isEmpty(_.compact(obj.data.non_ohip_keys))) { %}<ul class="pt-3 ps-2 list-group list-group-flush">Optional tests:
{% obj.data.non_ohip_keys.forEach(function(k, i) { %}<li class="list-group-item"><i class="me-2 fas fa-syringe text-blue"></i><span class="small">{{_.get(obj.data.td, k+'.n', k)}}: \${{_.get(obj.data.td, k+'.p')}}</span></li>{% }) %}<em class="text-xs">{% if (obj.data.use_insurance=='yes') { %}Uninsured fee applies{% } %}</em></ul>{% } %}<!-- {% if (['std-ug'].includes(window.adh_key)) { %}--><div class="rounded-3 p-3 mx-auto shadow-sm text-center bg-info-soft mt-3 small" style="max-width:400px;"><div class="mb-2 fw-semibold text-cyan-hard">Selected for you based on:</div><div class="d-flex flex-wrap justify-content-center gap-2"><span class="badge rounded-pill py-2" style="background-color:#F3F0FF;color:#5B3FCD;">How often you want to test</span><span class="badge rounded-pill py-2" style="background-color:#F3F0FF;color:#5B3FCD;">Tests you want</span></div></div><!-- {% } %}--></div></div><!-- {% } %}--><!-- {% if (obj.data.non_ohip_keys && !_.isEmpty(_.compact(obj.data.non_ohip_keys))) { %}--><p class="text-xs mt-n3 mb-4">Certain tests have an additional fee as {% if(data.use_insurance=='no'){ %}you indicated you would not be using OHIP/insurance.{% } else { %}they are not covered by OHIP.{% } %}<br>These fees are payable at the time of the lab visit directly to the lab testing company.<br>{% if(data.use_insurance=='yes'&&window.adh_key=='ped-trt'){ %}<a href='https://docs.teletest.ca/performance-and-enhancing-drugs-peds#test-frequency' target='_blank'>You can read more about why some testing is not insured through OHIP here.</a>{% } %}</p><!-- {% } %}--><!-- {% if (['labt','ped-trt','std-ug'].includes(window.adh_key)) { %}--><h3>What does a TeleTest panel provide?</h3><p>A TeleTest panel is more than just lab testing. We provide a 360° view of your health with expert physician interpretation and guidance.</p><p>By working with CPSO-certified physicians, TeleTest offers personalized medicine with guidance, prescriptions, and tests that are tailored to your specific needs.</p><div class="row justify-content-center mb-5"><div class="col-6"><div class="round card h-100 shadow-none bg-info-soft"><div class="card-body p-3 text-center"><h5>TeleTest</h5><div class="small"><div class="text-xs fw-600">Next appointment</div><div class="text-cyan-hard">today</div><div class="text-xs fw-600 mt-3">Online?</div><div class="text-cyan-hard">Yes</div><div class="text-xs fw-600 mt-3">Results</div><div class="text-cyan-hard">Available online in 2-3 days</div></div></div></div></div><div class="col-6"><div class="round card h-100 shadow-none border-0"><div class="card-body p-3 text-center"><h5>Walk-In</h5><div class="small"><div class="text-xs fw-600">Next appointment</div><div class="text-cyan-hard">2+ hour wait</div><div class="text-xs fw-600 mt-3">Online?</div><div class="text-cyan-hard">No</div><div class="text-xs fw-600 mt-3">Results</div><div class="text-cyan-hard">Delivered via phone call</div></div></div></div></div></div><!-- {% if (window.adh_key=='ped-trt') { %}--><h4>Your panel recommendation is based on:</h4><div class="round card mb-5"><div class="card-body p-3"><h5 class="text-cyan-hard baby-blue-divider-left">Your individual medical history</h5><h5 class="text-cyan-hard baby-blue-divider-left">Your goals</h5></div></div><!-- {% } %}--><!-- {% } %}--><div class="card mb-5"><div class="card-body p-0"><div class="accordion" id="accordionBlurbFaq"><div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm"><h2 class="accordion-header" id="faqHBlurb0"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCBlurb0" aria-expanded="false" aria-controls="faqCBlurb0">Why we’re trusted</button></h2><div class="accordion-collapse collapse" id="faqCBlurb0" aria-labelledby="faqHBlurb0" data-bs-parent="#accordionBlurbFaq"><div class="accordion-body">
        <p>We’ve treated over 50,000 patients and have a 4.9 star rating on Google Reviews.</p>
        <p>While other clinics may use nurse practitioners or physician assistants, all of our healthcare providers are CPSO licensed physicians.</p>
        <p>All of our physicians are familiar with the unique needs of our specialties.</p>
    </div></div></div><div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm"><h2 class="accordion-header" id="faqHBlurb1"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCBlurb1" aria-expanded="false" aria-controls="faqCBlurb1">What’s included</button></h2><div class="accordion-collapse collapse" id="faqCBlurb1" aria-labelledby="faqHBlurb1" data-bs-parent="#accordionBlurbFaq"><div class="accordion-body">
        <ul>{% if (['rx-rn','wh-pd'].includes(window.adh_key)) { %}<li>A consultation with an expert physician via secure messaging</li><li>Prescription sent to a pharmacy of your choice if approved</li>{% }else{ %}<li>An initial intake appointment via secure messaging</li><li>A requisition (form) for testing anywhere in Ontario</li><li>Result access via online portal</li><li>Follow-up appointments to discuss abnormal results &amp; next steps</li>{% } %}</ul>
    </div></div></div></div></div></div>`,
            };
            Formio.Templates.current['radio'] = {
                form: `<div
  class="form-radio radio"
  ref="radioGroup"
  role="{{ctx.component.type === 'selectboxes' ? 'group' : 'radiogroup'}}"
  aria-required="{{ctx.input.component.validate.required}}"
  aria-labelledby="l-{{ctx.instance.id}}-{{ctx.component.key}}"
  {% if (ctx.component.description) { %}
    aria-describedby="d-{{ctx.instance.id}}-{{ctx.component.key}}"
  {% } %}
>
  {% ctx.values.forEach(function(item) { %}
  {% if (ctx.component.type !== 'selectboxes') { %}
  <div class="{{ctx.input.attr.type}} {{ ctx.component.optionsLabelPosition && ctx.component.optionsLabelPosition !== 'right' ? 'pl-0' : ''}} form-check{{ctx.inline ? '-inline' : ''}}" ref="wrapper">
    <label class="form-check-label label-position-{{ ctx.component.optionsLabelPosition }}" for="{{ctx.instance.root && ctx.instance.root.id}}-{{ctx.id}}-{{ctx.row}}-{{item.value}}">
      {% if (ctx.component.optionsLabelPosition === 'left' || ctx.component.optionsLabelPosition === 'top') { %}
      <span>{{ctx.t(item.label, { _userInput: true })}}</span>
      {% } %}
      <{{ctx.input.type}}
        ref="input"
        {% for (var attr in ctx.input.attr) { %}
        {{attr}}="{{ctx.input.attr[attr]}}"
        {% } %}
        value="{{item.value}}"
        {% if (ctx.value && (ctx.value === item.value || (typeof ctx.value === 'object' && ctx.value.hasOwnProperty(item.value) && ctx.value[item.value]))) { %}
          checked=true
        {% } %}
        {% if (item.disabled) { %}
          disabled=true
        {% } %}
        id="{{ctx.instance.root && ctx.instance.root.id}}-{{ctx.id}}-{{ctx.row}}-{{item.value}}"
        role="{{ctx.component.type === 'selectboxes' ? 'checkbox' : 'radio'}}"
      >
      {% if (!ctx.component.optionsLabelPosition || ctx.component.optionsLabelPosition === 'right' || ctx.component.optionsLabelPosition === 'bottom') { %}
      <span>{{ctx.t(item.label, { _userInput: true })}}</span>
      {% } %}
    </label>
  </div>
  {% } else { %}
    <!-- adriang: add conditional visibility to individual selectbox values -->
    {% if (item.visible !== false) { %}
    <div class="form-check" ref="wrapper">
      <{{ctx.input.type}}
        class="form-check-input"
        ref="input"
        {% for (var attr in ctx.input.attr) { %}
        {{attr}}="{{ctx.input.attr[attr]}}"
        {% } %}
        value="{{item.value}}"
        {% if (ctx.value && (ctx.value === item.value || (typeof ctx.value === 'object' && ctx.value.hasOwnProperty(item.value) && ctx.value[item.value]))) { %}
          checked=true
        {% } %}
        {% if (item.disabled) { %}
          disabled=true
        {% } %}
        id="{{ctx.instance.root && ctx.instance.root.id}}-{{ctx.id}}-{{ctx.row}}-{{item.value}}"
        role="{{ctx.component.type === 'selectboxes' ? 'checkbox' : 'radio'}}"
      >
    </{{ctx.input.type}}>
  <label class="form-check-label" for="{{ctx.instance.root && ctx.instance.root.id}}-{{ctx.id}}-{{ctx.row}}-{{item.value}}">
    <span>{{ctx.t(item.label, { _userInput: true })}}</span>
  </label>
  </div>
  {% } %}
  <!-- end if item.visible -->
  {% } %}
  {% }) %}
</div>
`,
            };
        </script>
        <!-- {% endverbatim %}-->
        <script src="/js/adh-formio/adh-formio.js?v=1"></script>
        <script>
            let calBase = 'MMM DD, YYYY';
            let calConf = {
                sameDay: '[Today]',
                nextDay: '[Tomorrow]',
                nextWeek: calBase,
                lastDay: '[Yesterday]',
                lastWeek: calBase,
                sameElse: calBase,
            };
        </script>
        <script src="/js/adh-formio/{{adh.adh_key}}.js?v=1"></script>
        <script>
            let csrf = '{{csrf_token}}';
            let formioForm = {};
            Formio.setUser({ _id: 'none' });
            let f_api_url = window.location.protocol + '//' + window.location.host + '{{adh.url}}/';
            let s_api_url = `${f_api_url}submission`;
            let fj = { display: 'wizard', components: cfcs };
            Formio.createForm(document.getElementById('formio'), fj, {
                saveDraft: true,
                hooks: {
                    beforeSubmit: (submission, next) => {
                        submission.state = 'submitted';
                        console.log(`set sub state to: ${submission.state}`);
                        next();
                    },
                },
            }).then((form) => {
                //- redirect to next_url
                form.on('submitDone', function (submission) {
                    location.href = submission.next_url;
                });
                // Change URL: From Webform.js>setSrc
                form.setUrl(s_api_url);
                form.nosubmit = false;
                //- form.setSrc(s_api_url);
                form.draftEnabled = true;
                form.savingDraft = false;
                form.editMode = true;
                form.restoreDraft('none');
                formioForm = form;
                //- async call to save components
                $.ajax({ url: f_api_url, type: 'POST', contentType: 'application/json', data: JSON.stringify(fj) });
            });
            const FetchPlugin = {
                preRequest: function (requestArgs) {
                    return new Promise(function (resolve, reject) {
                        if (requestArgs.opts) {
                            requestArgs.opts.headers['X-CSRFToken'] = csrf;
                        }
                        resolve();
                    });
                },
            };
            Formio.registerPlugin(FetchPlugin, 'customfetch');
            $.ajaxSetup({
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('X-CSRFToken', csrf);
                },
            });
        </script>
    </body>
</html>

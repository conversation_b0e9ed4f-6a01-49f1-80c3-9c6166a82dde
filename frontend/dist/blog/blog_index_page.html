<!DOCTYPE html>
<!-- {% load template_extras %}-->
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="{{page.search_description}}" />
        <meta name="author" content="" />
        <!-- {% load wagtailcore_tags wagtailimages_tags %}-->
        <title>TeleTest Blog - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <a class="navbar-brand logo-text pe-3 ps-1 ps-lg-3" href="/">
                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                <span class="ms-3">TeleTest.ca</span>
            </a>
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i class="feather-lg" data-feather="menu"></i></button>
            <form class="form-inline me-auto d-none d-lg-block me-3" method="POST" action="/app/care/search/">
                <div class="input-group input-group-joined input-group-solid">
                    <input class="form-control pe-0" name="sq" type="search" placeholder="Search..." aria-label="Search" />
                    <div class="input-group-text"><i data-feather="search"></i></div>
                </div>
            </form>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto me-lg-5 me-1">
                <li class="nav-item dropdown no-caret me-3 d-lg-none">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="searchDropdown" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i data-feather="search"></i></a>
                    <div class="dropdown-menu dropdown-menu-end p-3 shadow animated--fade-in-up" aria-labelledby="searchDropdown">
                        <form class="form-inline me-auto w-100" method="POST" action="/app/care/search/">
                            <div class="input-group input-group-joined input-group-solid">
                                <input class="form-control pe-0" name="sq" type="search" placeholder="Search..." aria-label="Search" />
                                <div class="input-group-text"><i data-feather="search"></i></div>
                            </div>
                        </form>
                    </div>
                </li>
                <!-- {% if not user.is_authenticated %}-->
                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                <!-- {% else %}-->
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="/assets/img/icons/users/user-0c.svg" />
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">{{user.profile.name}}</div>
                                <div class="dropdown-user-details-email">{{user.email}}</div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/app/account-details/">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="/app/accounts/logout/">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
                <!-- {% endif %}-->
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <!-- if categories-->
                <!-- {% if logout_redirect and logout_redirect == False %}-->
                <!-- false-->
                <!-- {% else %}-->
                <!-- true-->
                {{ redirect_to_login_immediately }}
                <!-- {% endif %}-->
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <a class="nav-link" href="/app/patient-portal/">
                                <div class="nav-link-icon"><i data-feather="home"></i></div>
                                Patient Portal
                            </a>
                            <a class="nav-link" href="/app/contact/">
                                <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                Contact Us
                            </a>
                            <a class="nav-link" href="/app/about/">
                                <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                About Us
                            </a>
                            <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                FAQ
                            </a>
                            <a class="nav-link" href="/blog/">
                                <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                Blog
                            </a>
                            <a class="nav-link" href="/app/lab-testing/ON/cities/">
                                <div class="nav-link-icon"><i data-feather="clock"></i></div>
                                Lab Times
                            </a>
                            <!-- {% if user.is_authenticated %}-->
                            <a class="nav-link" href="/app/provider-info/">
                                <div class="nav-link-icon"><i class="fas fa-notes-medical"></i></div>
                                Provider Info
                            </a>
                            <!-- {% endif %}-->
                            <div class="sidenav-menu-heading">Healthcare</div>
                            <a class="nav-link" href="/app/categories/">
                                <div class="nav-link-icon"><i data-feather="layers"></i></div>
                                Categories
                            </a>
                            <!-- {% if not categories %}-->
                            <a class="nav-link" href="/app/care/std/">
                                <div class="nav-link-icon"><i class="fas fa-venus-mars"></i></div>
                                STD
                            </a>
                            <!-- {% else %} {% for category in categories %}-->
                            <a class="nav-link" href="{{category.url}}">
                                <div class="nav-link-icon"><i class="fas fa-{{category.icon}}"></i></div>
                                {{category.name}}
                            </a>
                            <!-- {% endfor %} {% endif %}-->
                            <div class="sidenav-menu-heading">Intake</div>
                            <a class="nav-link" href="/app/intake/">
                                <div class="nav-link-icon"><i class="fas fa-stethoscope"></i></div>
                                Virtual Care Assessment
                            </a>
                            <a class="nav-link" href="/app/account-details/">
                                <div class="nav-link-icon"><i data-feather="user"></i></div>
                                Account Details
                            </a>
                            <a class="nav-link" href="/app/find-location/lab/">
                                <div class="nav-link-icon"><i class="fas fa-search-location"></i></div>
                                Find Lab
                            </a>
                            <a class="nav-link" href="/app/payment/">
                                <div class="nav-link-icon"><i class="fas fa-credit-card"></i></div>
                                Checkout
                            </a>
                        </div>
                    </div>
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <!-- {% if user.is_authenticated %}-->
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title profileEmail"></div>
                            {{user.email}}
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-compact page-header-light border-bottom bg-white mb-2">
                        <div class="container-xl">
                            <div class="page-header-content">
                                <div class="row align-items-center justify-content-between pt-3">
                                    <div class="col-auto mb-3">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i class="fas fa-blog"></i></div>
                                            TeleTest Blog
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="container">
                        <div class="container px-5">
                            <!-- {% if categories %}-->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">Categories</h6>
                                            <div class="d-flex flex-wrap gap-2">
                                                <a class="btn btn-outline-primary btn-sm" href="{{page.url}}">All</a>
                                                <!-- {% for category in categories %}-->
                                                <a class="btn btn-outline-primary btn-sm" href="{{page.url}}categories/{{category.slug}}/">{{category.name}}</a>
                                                <!-- {% endfor %}-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- {% endif %}-->
                            <!-- {% if featured_post %}-->
                            <a class="card post-preview post-preview-featured lift mb-5 overflow-hidden" href="{% pageurl featured_post %}">
                                <div class="row g-0">
                                    <div class="col-lg-5">
                                        <!-- {% if featured_post.image %}{% image featured_post.image original as fp_img %}-->
                                        <div class="post-preview-featured-img" style='background-image: url("{{fp_img.url}}")'></div>
                                        <!-- {% else %}-->
                                        <div class="post-preview-featured-img" style="background-image: url('https://source.unsplash.com/vZJdYl5JVXY/660x360')"></div>
                                        <!-- {% endif %}-->
                                    </div>
                                    <div class="col-lg-7">
                                        <div class="card-body">
                                            <div class="py-5">
                                                <h5 class="card-title">{{featured_post.title}}</h5>
                                                <p class="card-text">{{ featured_post.introduction }}</p>
                                            </div>
                                            <hr />
                                            <div class="post-preview-meta">
                                                <img class="post-preview-meta-img" src="/assets/img/logo/avatar-512w.png" />
                                                <div class="post-preview-meta-details">
                                                    <div class="post-preview-meta-details-name">TeleTest Team</div>
                                                    <div class="post-preview-meta-details-date">{% if featured_post.date_published %}{{featured_post.date_published}}{% else %}{% now "M. j, Y" %}{% endif %} &middot; {{featured_post.read_time}} min read</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            <!-- {% endif %}-->
                            <!-- {% if tag %}-->
                            <div class="alert alert-info">Showing posts tagged with "{{tag.name}}"</div>
                            <!-- {% elif category %}-->
                            <div class="alert alert-info">Showing posts in category "{{category.name}}"</div>
                            <!-- {% endif %}-->
                            <div class="row gx-5">
                                <!-- {% for post in posts %}-->
                                <div class="col-md-6 col-xl-4 mb-5">
                                    <a class="card post-preview lift h-100" href="{% pageurl post %}">
                                        <!-- {% if post.image %}-->
                                        {% image post.image fill-317x173 class="card-img-top" %}
                                        <!-- {% else %}-->
                                        <img class="card-img-top" src="https://source.unsplash.com/KE0nC8-58MQ/660x360" alt="..." />
                                        <!-- {% endif %}-->
                                        <div class="card-body">
                                            <h5 class="card-title">{{ post.title }}</h5>
                                            <p class="card-text">{{ post.introduction|truncatewords:15 }}</p>
                                            <!-- {% if post.category %}-->
                                            <div class="badge bg-primary mb-2">{{post.category.name}}</div>
                                            <!-- {% endif %}-->
                                        </div>
                                        <div class="card-footer">
                                            <div class="post-preview-meta">
                                                <img class="post-preview-meta-img" src="/assets/img/logo/avatar-512w.png" />
                                                <div class="post-preview-meta-details">
                                                    <div class="post-preview-meta-details-name">TeleTest Team</div>
                                                    <div class="post-preview-meta-details-date">{{post.date_published}} &middot; {{post.read_time}} min read</div>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <!-- {% empty %}-->
                                <div class="col-12"><div class="alert alert-info text-center">No blog posts found.</div></div>
                                <!-- {% endfor %}-->
                            </div>
                            <!-- {% if posts.has_other_pages %}-->
                            <nav aria-label="Blog pagination">
                                <ul class="pagination pagination-blog justify-content-center">
                                    <!-- {% if posts.has_previous %}-->
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{posts.previous_page_number}}" aria-label="Previous"><span aria-hidden="true">«</span></a>
                                    </li>
                                    <!-- {% else %}-->
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#!" aria-label="Previous"><span aria-hidden="true">«</span></a>
                                    </li>
                                    <!-- {% endif %}-->
                                    <!-- {% for num in posts.paginator.page_range %}// {% if posts.number == num %}-->
                                    <li class="page-item active">
                                        <a class="page-link" href="?page={{num}}">{{num}}</a>
                                        <!-- {% elif num > posts.number|add:'-3' and num < posts.number|add:'3' %}-->
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{num}}">{{num}}</a>
                                        <!-- {% endif %}-->
                                    </li>
                                    <!-- {% endfor %}-->
                                    <!-- {% if posts.has_next %}-->
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{posts.next_page_number}}" aria-label="Next"><span aria-hidden="true">»</span></a>
                                    </li>
                                    <!-- {% else %}-->
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#!" aria-label="Next"><span aria-hidden="true">»</span></a>
                                    </li>
                                    <!-- {% endif %}-->
                                </ul>
                            </nav>
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container">
                        <div class="row mt-0">
                            <div class="col-md-12">
                                <div class="logo-listing-wrp border-top">
                                    <div class="d-flex flex-wrap align-items-center mt-4 justify-content-center">
                                        <a href="https://www.legitscript.com/websites/?checker_keywords=teletest.ca" target="_blank" title="Verify LegitScript Approval" rel="nofollow"><img src="https://static.legitscript.com/seals/10847275.png" alt="LegitScript approved" width="120" border="0" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center text-md-start text-center mt-3 mt-md-4">
                            <div class="col-md-6 small text-dark text-md-end">
                                Copyright &copy; TeleTest
                                <script>
                                    document.write(new Date().getFullYear());
                                </script>
                            </div>
                            <div class="col-md-6 small">
                                <a href="/privacy-policy.html">Privacy Policy</a>
                                &middot;
                                <a href="/terms-of-service.html">Terms of Service</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script src="/js/scripts.js"></script>
        <!-- {% if pre_req %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('requiredModal'), { backdrop: 'static', keyboard: false });
            modal.show();
            if ($('#loading-spinner').length) {
                $('#loading-spinner').hide();
            }
        </script>
        <!-- {% else %}-->
        <!-- {% if coupon_text %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('couponModal'), {});
            modal.show();
            let url = new URL(location.href);
            url.searchParams.delete('coupon');
            window.history.pushState({}, document.title, url);
        </script>
        <!-- {% endif %}-->
        <!-- {% endif %}-->
    </body>
</html>

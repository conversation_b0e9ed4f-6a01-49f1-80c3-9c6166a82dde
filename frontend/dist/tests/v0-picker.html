<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="Choose your product - Select the option that works best for you" />
        <meta name="author" content="" />
        <!-- {% load template_extras %}-->
        <title>{{picker.name}} - Product Selection - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <style>
            /* Use existing codebase pattern for checked radio styling */
            input[name='change-sku']:checked + .form-check-label .list-group-item,
            .list-group-item:has(input[name='change-sku']:checked) {
                border: 2px solid var(--bs-primary);
                box-shadow: 0 0.15rem 0.5rem 0 rgba(0, 82, 204, 0.5);
                transition: border 0.3s;
            }
        </style>
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body>
        <div id="layoutDefault">
            <div id="layoutDefault_content">
                <main>
                    <div class="container pb-5">
                        <!-- {% with product=cart.product %}{% with sub_period=cart.sub_period %}{% with picker_description=product.picker.description %}-->
                        <!-- {% make_composite_product product sub_period as cp %}-->
                        <form id="app-form" method="POST">
                            {% csrf_token %}
                            <input type="hidden" name="selected-sku-period" value="{{cp.key}}" />
                            <div class="container-fluid px-0">
                                <div class="row justify-content-center">
                                    <div class="col-12 col-md-9 col-lg-7 col-xl-6 px-3 pt-4">
                                        <div class="text-center mb-4">
                                            <!-- {% if picker_description %}-->
                                            <h4 class="text-dark fw-medium">{{picker.name}}</h4>
                                            <!-- {% else %}-->
                                            <h4 class="text-dark fw-medium">Testing and/or Treatment</h4>
                                            <!-- {% endif %}-->
                                        </div>
                                        <div class="row justify-content-center">
                                            <div class="col-md-9">
                                                <div class="card round bg-blue-soft mb-3" id="final-product">
                                                    <div class="card-header">
                                                        <div class="d-flex justify-content-between align-items-center text-blue-hard">
                                                            <div class="div">{{cp.sp_name}}</div>
                                                            <div class="badge bg-green-soft rounded-pill badge-marketing badge-md text-green">{{cp.price_display}}</div>
                                                        </div>
                                                    </div>
                                                    <div class="card-body px-0 pt-2 bg-white">
                                                        <div class="px-3 text-gray-700">{{cp.info|safe}}</div>
                                                        <ul class="ps-2 list-group list-group-flush">
                                                            <!-- {% for b in bullets %}{% if b.product_page != False %}-->
                                                            <li class="list-group-item">
                                                                <!-- {%   if b.test_type == 'coupon' %} -->
                                                                <i class="me-2 fas fa-money-bill-wave text-green"></i>
                                                                <!-- {% elif b.test_type == 'blood' %} -->
                                                                <i class="me-2 fas fa-syringe text-green"></i>
                                                                <!-- {% elif b.test_type == 'ecg' %} -->
                                                                <i class="me-2 fas fa-heart-pulse text-green"></i>
                                                                <!-- {% elif b.test_type == 'imaging' %} -->
                                                                <i class="me-2 fas fa-x-ray text-green"></i>
                                                                <!-- {% elif b.test_type == 'urine' %} -->
                                                                <i class="me-2 fas fa-prescription-bottle text-green"></i>
                                                                <!-- {% elif b.test_type == 'note' %} -->
                                                                <i class="me-2 fas fa-note-sticky text-green"></i>
                                                                <!-- {% elif 'swab' in b.test_type %} -->
                                                                <i class="me-2 fas fa-broom text-green"></i>
                                                                <!-- {% elif 'ship' in b.test_type %} -->
                                                                <i class="me-2 fas fa-shipping-fast text-green"></i>
                                                                <!-- {% elif 'prescription' in b.test_type %} -->
                                                                <i class="me-2 fas fa-capsules text-green"></i>
                                                                <!-- {% else %} -->
                                                                <i class="me-2 fas fa-vials text-green"></i>
                                                                <!-- {% endif %} -->
                                                                <span class="small">{{b.name}}</span>
                                                            </li>
                                                            <!-- {% endif %}{% endfor %}-->
                                                        </ul>
                                                        <!-- {% if picker.footnote %}-->
                                                        <div class="px-3 py-2"><p class="small text-gray-600 fst-italic mb-0">* {{picker.footnote}}</p></div>
                                                        <!-- {% endif %}-->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="rounded-3 p-3 mx-auto text-center bg-gray-200 mt-3 small" style="max-width: 400px">
                                            <div class="mb-2 fw-semibold text-gray-600">Selected for you based on:</div>
                                            <div class="d-flex justify-content-center gap-2 flex-wrap">
                                                <!-- {% if picker.key == 'mh-hl' %}-->
                                                <span class="badge bg-white text-dark px-2 py-1 rounded-pill">Hair goals</span>
                                                <!-- {% else %}-->
                                                <span class="badge bg-white text-dark px-2 py-1 rounded-pill">How frequently you want to test</span>
                                                <span class="badge bg-white text-dark px-2 py-1 rounded-pill">Selected test/treatment</span>
                                                <!-- {% endif %}-->
                                            </div>
                                        </div>
                                        <div class="card rounded-3 my-3">
                                            <div class="card-body p-4">
                                                <h2 class="h5 fw-medium mb-2">Details</h2>
                                                <!-- {% if picker.key == 'mh-hl' %}-->
                                                <p class="small mb-3">Bills and ships every 3 months, includes medication, shipping, optional lab testing to understand the root cause of your hair loss, unlimited consultations and all necessary follow-up.</p>
                                                <div class="row border-top border-bottom border-light mb-3">
                                                    <div class="col-4 py-3 text-center border-end border-light">
                                                        <p class="text-muted small mb-1">Type</p>
                                                        <p class="small fw-medium">Pill</p>
                                                    </div>
                                                    <div class="col-4 py-3 text-center border-end border-light">
                                                        <p class="text-muted small mb-1">Take</p>
                                                        <p class="small fw-medium">Once daily</p>
                                                    </div>
                                                    <div class="col-4 py-3 text-center">
                                                        <p class="text-muted small mb-1">Results in</p>
                                                        <p class="small fw-medium">3-6 mo.*</p>
                                                    </div>
                                                </div>
                                                <p class="text-muted text-xs mb-3">*Timelines may vary per individual</p>
                                                <!-- {% else %}-->
                                                <p class="small mb-3">{% if sub_period == 'month' %} Charged once per month, includes unlimited consultations and all necessary follow-up. {% elif sub_period == '3months' %} Subscription charged once every 3 months, includes one consultation and all necessary follow-up per 3 months. {% elif sub_period == '6months' %} Subscription charged once every 6 months, includes one consultation and all necessary follow-up per 6 months. {% elif sub_period == 'once' %} Charged once, includes one consultation and follow-up if appropriate. {% endif %}</p>
                                                <div class="row border-top border-bottom border-light mb-3">
                                                    <div class="col-4 py-3 text-center border-end border-light">
                                                        <p class="text-muted small mb-1">Testing</p>
                                                        <p class="small fw-medium">Lab visit</p>
                                                    </div>
                                                    <div class="col-4 py-3 text-center border-end border-light">
                                                        <p class="text-muted small mb-1">Consultation</p>
                                                        <p class="small fw-medium">Online messaging</p>
                                                    </div>
                                                    <div class="col-4 py-3 text-center">
                                                        <p class="text-muted small mb-1">Results</p>
                                                        <p class="small fw-medium">2-3 days*</p>
                                                    </div>
                                                </div>
                                                <p class="text-muted text-xs mb-3">*Timelines may vary and are dependent on tests ordered</p>
                                                <!-- {% endif %}-->
                                                <div class="accordion mt-4" id="accordionDetails">
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="detailsH1"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#detailsC1" aria-expanded="false" aria-controls="detailsC1">Why we're trusted</button></h2>
                                                        <div class="accordion-collapse collapse" id="detailsC1" aria-labelledby="detailsH1" data-bs-parent="#accordionDetails">
                                                            <div class="accordion-body">
                                                                <p>We've treated over 35,000 patients and have a 4.9 star rating on Google Reviews.</p>
                                                                <p>While other clinics may use nurse practitioners or physician assistants, all of our healthcare providers are CPSO licensed physicians.</p>
                                                                <p>All of our physicians are familiar with the unique needs of our specialties.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="detailsH2"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#detailsC2" aria-expanded="false" aria-controls="detailsC2">What's included</button></h2>
                                                        <div class="accordion-collapse collapse" id="detailsC2" aria-labelledby="detailsH2" data-bs-parent="#accordionDetails">
                                                            <div class="accordion-body">
                                                                <ul>
                                                                    <li>An initial intake appointment via secure messaging</li>
                                                                    <!-- {% if picker.key == 'mh-hl' %}-->
                                                                    <li>Hair loss prescription medication</li>
                                                                    <li>Medication shipping</li>
                                                                    <li>Optional lab testing to understand the root cause of your hair loss</li>
                                                                    <!-- {% else %}-->
                                                                    <li>A requisition (form) for testing anywhere in Ontario</li>
                                                                    <!-- {% endif %}-->
                                                                    <li>Result access via online portal</li>
                                                                    <li>Follow-up appointments to discuss abnormal results &amp; next steps</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- {% if picker.key == 'mh-hl' %}-->
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="detailsH3"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#detailsC3" aria-expanded="false" aria-controls="detailsC3">What to expect</button></h2>
                                                        <div class="accordion-collapse collapse" id="detailsC3" aria-labelledby="detailsH3" data-bs-parent="#accordionDetails">
                                                            <div class="accordion-body">
                                                                <p class="mb-2">
                                                                    <strong>1-3 months:</strong>
                                                                    Too early to see results at this time
                                                                </p>
                                                                <p class="mb-2">
                                                                    <strong>3-6 months:</strong>
                                                                    Shedding may slow down
                                                                </p>
                                                                <p class="mb-0">
                                                                    <strong>6+ months:</strong>
                                                                    Hair loss may reach a plateau, with lack of noticeable shedding
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="detailsH4"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#detailsC4" aria-expanded="false" aria-controls="detailsC4">Side effects</button></h2>
                                                        <div class="accordion-collapse collapse" id="detailsC4" aria-labelledby="detailsH4" data-bs-parent="#accordionDetails">
                                                            <div class="accordion-body">
                                                                <p class="mb-3">Finasteride reduces DHT (a hormone that causes hair loss in men) in the scalp</p>
                                                                <ul class="mb-0">
                                                                    <li>
                                                                        In
                                                                        <a href="https://www.accessdata.fda.gov/spl/data/3c8dff7e-41ab-46db-bacf-c41cc237f9d9/3c8dff7e-41ab-46db-bacf-c41cc237f9d9.xml#section-6.1" target="_blank">clinical trials,</a>
                                                                        a small number of men experienced certain sexual side effects on finasteride, occurring in less than 2% of men.
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- {% endif %}-->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card rounded-3 mb-3">
                                            <div class="card-body p-4">
                                                <h3 class="h5 fw-medium mb-3">FAQs</h3>
                                                <div class="accordion" id="accordionFaq">
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="faqH0"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC0" aria-expanded="false" aria-controls="faqC0">How can I reimburse with my private insurance or Health Spending Account (HSA)?</button></h2>
                                                        <div class="accordion-collapse collapse" id="faqC0" aria-labelledby="faqH0" data-bs-parent="#accordionFaq">
                                                            <div class="accordion-body">
                                                                <p>Most insurance plans cover online/virtual medical consultation with a physician. We will send you a receipt for your consultation or subscription, which you can use to seek reimbursement from your private insurance or HSA.</p>
                                                                <p>If you require specific details just email <NAME_EMAIL> for a detailed receipt suitable for your benefits claims.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="faqH1"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC1" aria-expanded="false" aria-controls="faqC1">How do I get my lab results? How long does it take?</button></h2>
                                                        <div class="accordion-collapse collapse" id="faqC1" aria-labelledby="faqH1" data-bs-parent="#accordionFaq">
                                                            <div class="accordion-body">
                                                                <p>All lab results are available via our online portal, unlike old-school doctor's offices no paper or phones are required.</p>
                                                                <p>We have a custom system that constantly monitors when lab results are ready and alerts you via email.</p>
                                                                <p>We have the fastest turnaround times in Ontario.</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="faqH2"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC2" aria-expanded="false" aria-controls="faqC2">Are video visits required?</button></h2>
                                                        <div class="accordion-collapse collapse" id="faqC2" aria-labelledby="faqH2" data-bs-parent="#accordionFaq">
                                                            <div class="accordion-body">
                                                                <p>
                                                                    <strong>No video visit is required;</strong>
                                                                    a provider will review your information 100% online. If prescribed, you'll get online access to message your provider.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item border-top-0 border-start-0 border-end-0 border-bottom-sm">
                                                        <h2 class="accordion-header" id="faqH3"><button class="accordion-button py-3 fw-bold text-gray-800 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqC3" aria-expanded="false" aria-controls="faqC3">Will my current doctor find out about TeleTest? Will this affect roster status?</button></h2>
                                                        <div class="accordion-collapse collapse" id="faqC3" aria-labelledby="faqH3" data-bs-parent="#accordionFaq">
                                                            <div class="accordion-body">
                                                                <p>
                                                                    <strong>No.</strong>
                                                                    TeleTest appointments are not OHIP-funded &amp; will not affect roster status.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card rounded-3 mb-10">
                                            <div class="card-body p-4">
                                                <!-- {% if picker.key == 'mh-hl' %}-->
                                                <h3 class="h5 fw-medium mb-3">What does a TeleTest subscription provide?</h3>
                                                <p class="small mb-4">A TeleTest subscription is more than just medication delivery. We provide a 360° view of your health with expert physician interpretation and guidance.</p>
                                                <p class="small mb-4">By working with CPSO-certified physicians, TeleTest offers personalized medication (including free shipping) with guidance, prescriptions, and tests that are tailored to your specific needs.</p>
                                                <p class="small mb-4">We bundle everything in one seamless package with a single, affordable monthly price.</p>
                                                <!-- {% else %}-->
                                                <h3 class="h5 fw-medium mb-3">What does a TeleTest panel provide?</h3>
                                                <p class="small mb-4">A TeleTest panel is more than just lab testing. We provide a 360° view of your health with expert physician interpretation and guidance.</p>
                                                <p class="small mb-4">By working with CPSO-certified physicians, TeleTest offers personalized medicine with guidance, prescriptions, and tests that are tailored to your specific needs.</p>
                                                <div class="row justify-content-center mb-4">
                                                    <div class="col-6">
                                                        <div class="card h-100 shadow-none bg-info-soft rounded">
                                                            <div class="card-body p-3 text-center">
                                                                <h5 class="small fw-medium">TeleTest</h5>
                                                                <div class="small">
                                                                    <div class="text-xs fw-600">Next appointment</div>
                                                                    <div class="text-cyan-hard">today</div>
                                                                    <div class="text-xs fw-600 mt-3">Online?</div>
                                                                    <div class="text-cyan-hard">Yes</div>
                                                                    <div class="text-xs fw-600 mt-3">Results</div>
                                                                    <div class="text-cyan-hard">Available online in 2-3 days</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="card h-100 shadow-none border-0 rounded">
                                                            <div class="card-body p-3 text-center">
                                                                <h5 class="small fw-medium">Walk-In</h5>
                                                                <div class="small">
                                                                    <div class="text-xs fw-600">Next appointment</div>
                                                                    <div class="text-cyan-hard">2+ hour wait</div>
                                                                    <div class="text-xs fw-600 mt-3">Online?</div>
                                                                    <div class="text-cyan-hard">No</div>
                                                                    <div class="text-xs fw-600 mt-3">Results</div>
                                                                    <div class="text-cyan-hard">Delivered via phone call</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- {% endif %}-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="fixed-bottom bg-white p-3 border-top">
                                <div class="container-fluid">
                                    <div class="row justify-content-center">
                                        <div class="col-12 col-md-6 col-lg-4">
                                            <div class="d-flex flex-column gap-3">
                                                <div class="d-grid">
                                                    <button class="btn btn-dark btn-lg rounded-pill py-3 fw-500 text-lg d-flex justify-content-between" id="continue-btn" type="submit" role="button" name="continue-button">
                                                        <i class="fas fa-flask me-2"></i>
                                                        Continue
                                                        <i class="feather-lg" data-feather="arrow-right"></i>
                                                    </button>
                                                </div>
                                                <!-- {% if picker.products.count > 1 or product.sub_periods|length > 1 %}-->
                                                <button class="btn btn-link text-gray-600 p-2" type="button" data-bs-toggle="modal" data-bs-target="#treatmentOptionsModal">See other options</button>
                                                <!-- {% endif %}-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="modal fade" id="treatmentOptionsModal" tabindex="-1" aria-labelledby="treatmentOptionsModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="treatmentOptionsModalLabel">Choose an option</h5>
                                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="modal-form" method="POST">
                                            {% csrf_token %}
                                            <div class="list-group rounded-0">
                                                <!-- {% for p in products %}{% for x in p.sub_periods %}{% if x == 'once' or p.sub_product %}-->
                                                <!-- {% make_composite_product p x as cp %}-->
                                                <div class="list-group-item list-group-item-action treatment-option">
                                                    <div class="form-check d-flex w-100 justify-content-between align-items-center">
                                                        <div class="flex-grow-1"><input class='form-check-input' type='radio' role='radio' name='change-sku-period' value='{{cp.key}}' id='modal-sku-{{p.id}}-{{x}}' ref='input'{% if p.id == product.id and x == sub_period %}checked{% endif %}></div>
                                                        <label class="form-check-label flex-grow-1" for="modal-sku-{{p.id}}-{{x}}">
                                                            <div class="row">
                                                                <div class="col-auto"><h6 class="mb-1">{{cp.sp_name}}</h6></div>
                                                                <div class="col text-end"><small>{{cp.price_display}}</small></div>
                                                            </div>
                                                            <div class="row"><p class="mb-1 small">{{cp.info|safe}}</p></div>
                                                        </label>
                                                    </div>
                                                </div>
                                                <!-- {% endif %}{% endfor %}{% endfor %}-->
                                            </div>
                                        </form>
                                        <!-- {% if picker.footnote %}-->
                                        <div class="px-3 py-2">
                                            <p class="small text-gray-600 fst-italic mb-0">* {{picker.footnote}}</p>
                                            <p class="small text-gray-600 fst-italic mb-0">* All plans include follow-up if appropriate</p>
                                        </div>
                                        <!-- {% endif %}-->
                                    </div>
                                    <div class="modal-footer">
                                        <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                                        <button class="btn btn-primary" id="selectTreatmentBtn" type="submit" form="modal-form">Select This Option</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- {% endwith %}{% endwith %}{% endwith %}-->
                    </div>
                </main>
            </div>
            <div id="layoutDefault_footer"></div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script>
            var uuid = readFromStorage('uuid');
            $.ajax({
                type: 'POST',
                url: `/app/log-row/`,
                contentType: 'application/json',
                dataType: 'json',
                data: JSON.stringify({
                    uuid: uuid,
                    href: window.location.href,
                    HTTP_REFERER: document.referrer,
                }),
                success: function (data) {
                    if (data['uuid']) {
                        uuid = data['uuid'];
                        writeToStorage('uuid', uuid);
                    }
                    if (data['gclid']) {
                        writeToStorage('gclid', data['gclid']);
                    }
                },
            });
        </script>
        <script src="/js/scripts.js"></script>
    </body>
</html>

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>CodePen - Muuri - Grid Demo ( 0.4.0 )</title>
        <link href="/assets/demo/muuri-grid-demo.css" rel="stylesheet" crossorigin="anonymous" />
    </head>
    <section class="grid-demo">
        <h2 class="section-title"><span>Grid Demo</span></h2>
        <div class="controls cf">
            <div class="control search">
                <div class="control-icon"><i class="material-icons">&#xE8B6;</i></div>
                <input class="control-field search-field form-control" type="text" name="search" placeholder="Search..." />
            </div>
            <div class="control filter">
                <div class="control-icon"><i class="material-icons">&#xE152;</i></div>
                <div class="select-arrow"><i class="material-icons">&#xE313;</i></div>
                <select class="control-field filter-field form-control">
                    <option value="" selected="">All</option>
                    <option value="red">Red</option>
                    <option value="blue">Blue</option>
                    <option value="green">Green</option>
                </select>
            </div>
            <div class="control sort">
                <div class="control-icon"><i class="material-icons">&#xE164;</i></div>
                <div class="select-arrow"><i class="material-icons">&#xE313;</i></div>
                <select class="control-field sort-field form-control">
                    <option value="order" selected="">Drag</option>
                    <option value="title">Title (drag disabled)</option>
                    <option value="color">Color (drag disabled)</option>
                </select>
            </div>
            <div class="control layout">
                <div class="control-icon"><i class="material-icons">&#xE871;</i></div>
                <div class="select-arrow"><i class="material-icons">&#xE313;</i></div>
                <select class="control-field layout-field form-control">
                    <option value="left-top" selected="">Left Top</option>
                    <option value="left-top-fillgaps">Left Top (fill gaps)</option>
                    <option value="right-top">Right Top</option>
                    <option value="right-top-fillgaps">Right Top (fill gaps)</option>
                    <option value="left-bottom">Left Bottom</option>
                    <option value="left-bottom-fillgaps">Left Bottom (fill gaps)</option>
                    <option value="right-bottom">Right Bottom</option>
                    <option value="right-bottom-fillgaps">Right Bottom (fill gaps)</option>
                </select>
            </div>
        </div>
        <div class="grid"></div>
        <div class="grid-footer">
            <button class="add-more-items btn btn-primary">
                <i class="material-icons">&#xE145;</i>
                Add more items
            </button>
        </div>
    </section>
    <!-- partial-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/velocity/1.5.0/velocity.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/muuri/0.4.0/muuri.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="/assets/demo/muuri-grid-demo.js"></script>
</html>

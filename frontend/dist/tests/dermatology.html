<!DOCTYPE html>
<!-- {% load template_extras %}-->
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>{% if category %}{{category.name}}{% else %}{% if category_key == 'search' %}Product search{% else %}{{category_key|format_formio_val}}{% endif %}{% endif %} - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <a class="navbar-brand logo-text pe-3 ps-1 ps-lg-3" href="/">
                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                <span class="ms-3">TeleTest.ca</span>
            </a>
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i class="feather-lg" data-feather="menu"></i></button>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto me-lg-5 me-1">
                <!-- {% if not user.is_authenticated %}-->
                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                <!-- {% else %}-->
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="/assets/img/icons/users/user-0c.svg" />
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">{{user.profile.name}}</div>
                                <div class="dropdown-user-details-email">{{user.email}}</div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/app/account-details/">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="/app/accounts/logout/">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
                <!-- {% endif %}-->
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <!-- if categories-->
                <!-- {% if logout_redirect and logout_redirect == False %}-->
                <!-- false-->
                <!-- {% else %}-->
                <!-- true-->
                {{ redirect_to_login_immediately }}
                <!-- {% endif %}-->
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <a class="nav-link" href="/app/patient-portal/">
                                <div class="nav-link-icon"><i data-feather="home"></i></div>
                                Patient Portal
                            </a>
                            <a class="nav-link" href="/app/contact/">
                                <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                Contact Us
                            </a>
                            <a class="nav-link" href="/app/about/">
                                <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                About Us
                            </a>
                            <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                FAQ
                            </a>
                            <a class="nav-link" href="/blog/">
                                <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                Blog
                            </a>
                            <a class="nav-link" href="/app/lab-testing/ON/cities/">
                                <div class="nav-link-icon"><i data-feather="clock"></i></div>
                                Lab Times
                            </a>
                            <!-- {% if user.is_authenticated %}-->
                            <a class="nav-link" href="/app/provider-info/">
                                <div class="nav-link-icon"><i class="fas fa-notes-medical"></i></div>
                                Provider Info
                            </a>
                            <!-- {% endif %}-->
                            <div class="sidenav-menu-heading">Healthcare</div>
                            <a class="nav-link" href="/app/categories/">
                                <div class="nav-link-icon"><i data-feather="layers"></i></div>
                                Categories
                            </a>
                            <!-- {% if not categories %}-->
                            <a class="nav-link" href="/app/care/std/">
                                <div class="nav-link-icon"><i class="fas fa-venus-mars"></i></div>
                                STD
                            </a>
                            <!-- {% else %} {% for category in categories %}-->
                            <a class="nav-link" href="{{category.url}}">
                                <div class="nav-link-icon"><i class="fas fa-{{category.icon}}"></i></div>
                                {{category.name}}
                            </a>
                            <!-- {% endfor %} {% endif %}-->
                            <div class="sidenav-menu-heading">Intake</div>
                            <a class="nav-link" href="/app/intake/">
                                <div class="nav-link-icon"><i class="fas fa-stethoscope"></i></div>
                                Virtual Care Assessment
                            </a>
                            <a class="nav-link" href="/app/account-details/">
                                <div class="nav-link-icon"><i data-feather="user"></i></div>
                                Account Details
                            </a>
                            <a class="nav-link" href="/app/find-location/lab/">
                                <div class="nav-link-icon"><i class="fas fa-search-location"></i></div>
                                Find Lab
                            </a>
                            <a class="nav-link" href="/app/payment/">
                                <div class="nav-link-icon"><i class="fas fa-credit-card"></i></div>
                                Checkout
                            </a>
                        </div>
                    </div>
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <!-- {% if user.is_authenticated %}-->
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title profileEmail"></div>
                            {{user.email}}
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-compact page-header-light border-bottom bg-white mb-2">
                        <div class="container-xl">
                            <div class="page-header-content">
                                <div class="row align-items-center justify-content-between pt-3">
                                    <div class="col-auto mb-3">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i class="fas fa-{% if category %}{{category.icon}}{% else %}square-poll-horizontal{% endif %}"></i></div>
                                            {% if category %}{{category.name}}{% else %}{% if category_key == 'search' %}Product search{% else %}{{category_key|format_formio_val}}{% endif %}{% endif %}
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="container">
                        <div class="card card-waves">
                            <div class="card-body px-4 pt-3 pb-0">
                                <div class="row align-items-center justify-content-between">
                                    <div class="col-lg-8">
                                        <ul class="list-group-info">
                                            <li class="list-group-item">
                                                <i class="me-2" data-feather="info" style="min-width: 16px"></i>
                                                Click a card to select, then click continue below
                                            </li>
                                            <li class="list-group-item">
                                                <i class="me-2" data-feather="info" style="min-width: 16px"></i>
                                                All prices are in CAD &amp; are for secure messaging
                                            </li>
                                        </ul>
                                        <div class="mb-2">Search all products including prescription medication, testing panels, and health conditions:</div>
                                        <form id="search-form" method="POST" action="/app/care/search/">
                                            {% csrf_token %}
                                            <div class="shadow rounded mb-5">
                                                <div class="input-group input-group-joined input-group-joined-xl border-0">
                                                    <input class="form-control me-0" id="search" name="sq" value="{{sq}}" type="text" placeholder="Search..." aria-label="Search" autofocus />
                                                    <span class="input-group-text"><i data-feather="search"></i></span>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="col d-none d-lg-block"><img class="img-fluid" src="/assets/img/freepik/online-test-pana.svg" /></div>
                                </div>
                            </div>
                        </div>
                        <!-- {% if cart and cart.product %}-->
                        <!-- {% if cart %}-->
                        <div class="card card-collapsable mt-3">
                            <a class="card-header collapsed" href="#collapseShoppingCart" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="collapseShoppingCart">
                                <span>
                                    <i class="vam me-l" data-feather="shopping-cart"></i>
                                    Cart
                                    <!-- {% if cart.products %}-->
                                    <div class="no-hover btn bg-white btn-icon btn-xs border-md btn-outline-blue font-weight-700 px-1 ms-2">{{cart.products|length}}</div>
                                    <!-- {% endif %}-->
                                </span>
                                <div class="card-collapsable-arrow"><i class="fas fa-chevron-down"></i></div>
                            </a>
                            <div class="collapse" id="collapseShoppingCart">
                                <div class="card-body">
                                    <!-- {% with cart_completed=cart.completed %}-->
                                    <!-- {% if cart.products %}-->
                                    <form id="shopping-cart-form" method="POST">
                                        {% csrf_token %} {% with product=cart.product %}{% with sub_period=cart.sub_period %}{% make_composite_product product sub_period as cp %}
                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sq avatar-xl bg-white flex-shrink-0 mx-2">
                                                    <!-- {%   if cp.test_type == 'coupon' %} -->
                                                    <i class="me-2 fas fa-money-bill-wave undefined"></i>
                                                    <!-- {% elif cp.test_type == 'blood' %} -->
                                                    <i class="me-2 fas fa-syringe undefined"></i>
                                                    <!-- {% elif cp.test_type == 'ecg' %} -->
                                                    <i class="me-2 fas fa-heart-pulse undefined"></i>
                                                    <!-- {% elif cp.test_type == 'imaging' %} -->
                                                    <i class="me-2 fas fa-x-ray undefined"></i>
                                                    <!-- {% elif cp.test_type == 'urine' %} -->
                                                    <i class="me-2 fas fa-prescription-bottle undefined"></i>
                                                    <!-- {% elif cp.test_type == 'note' %} -->
                                                    <i class="me-2 fas fa-note-sticky undefined"></i>
                                                    <!-- {% elif 'swab' in cp.test_type %} -->
                                                    <i class="me-2 fas fa-broom undefined"></i>
                                                    <!-- {% elif 'ship' in cp.test_type %} -->
                                                    <i class="me-2 fas fa-shipping-fast undefined"></i>
                                                    <!-- {% elif 'prescription' in cp.test_type %} -->
                                                    <i class="me-2 fas fa-capsules undefined"></i>
                                                    <!-- {% else %} -->
                                                    <i class="me-2 fas fa-vials undefined"></i>
                                                    <!-- {% endif %} -->
                                                </div>
                                                <div class="d-flex flex-column font-weight-bold flex-shrink-1">
                                                    <div class="small text-dark line-height-normal mb-1" href="#!">{{cp.name_sp_name}}</div>
                                                    <div class="small text-muted line-height-normal">{{cp.invoice_description}}</div>
                                                    <!-- {% if cp.category == 'uninsured_fee' %}-->
                                                    <div>
                                                        <a class="btn btn-xs btn-link-primary" role="button" href="/app/find-location/lab/">
                                                            <i class="me-2 fa-solid fa-location-dot"></i>
                                                            Change Lab Location
                                                        </a>
                                                        <span class="d-none d-lg-inline-block">-</span>
                                                        <!-- {% if not self.valid_insurance %}-->
                                                        <a class="btn btn-xs btn-link-primary" role="button" href="/app/account-details/">
                                                            <i class="me-2 fa-solid fa-user"></i>
                                                            Add Missing Insurance
                                                        </a>
                                                        <!-- {% endif %}-->
                                                    </div>
                                                    <!-- {% elif cp.test_type != 'shipping' %}-->
                                                    <!-- {% if questionnaire.paid is not None  %}-->
                                                    <a class="btn btn-sm btn-link-primary text-start" role="button" href='{% url "patient-portal" %}'>
                                                        <i class="me-2 fas fa-stethoscope"></i>
                                                        <div class="text-xs">You cannot select another test or treatment until this is completed</div>
                                                    </a>
                                                    <!-- {% else %}-->
                                                    <button class="btn btn-xs btn-link-primary" type="submit" role="button" name="remove-cart-item" value="{{cp.key}}">
                                                        <i class="me-2 far fa-trash-alt"></i>
                                                        Remove
                                                    </button>
                                                    <!-- {% endif %}-->
                                                    <!-- {% endif %}-->
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center">{{cp.price_display}}</div>
                                        </div>
                                        {% endwith %}{% endwith %}
                                    </form>
                                    <!-- {% else %}-->
                                    <p><a href='{% url "categories" %}'>Select your healthcare category to get started.</a></p>
                                    <p>Your healthcare category will show here when selected.</p>
                                    <!-- {% endif %}-->
                                    <!-- {% endwith %}-->
                                </div>
                            </div>
                        </div>
                        <!-- {% endif %}-->
                        <!-- {% endif %}-->
                        <!-- {% if pre_req %}-->
                        <!-- pre_req == {{pre_req}}-->
                        <div class="modal fade" id="requiredModal" tabindex="-1" role="dialog" aria-labelledby="requiredModalLabel" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="text-center modal-title" id="requiredModalLabel">
                                            <!-- {% if pre_req == 'purchase' %}-->
                                            This Has Already Been Paid For
                                            <!-- {% elif pre_req == 'vca' %}-->
                                            Please Complete Intake Form
                                            <!-- {% elif pre_req == 'profile' %}-->
                                            Please Complete Account Details
                                            <!-- {% else %}-->
                                            Please Choose a Product
                                            <!-- {% endif %}-->
                                        </h5>
                                    </div>
                                    <div class="modal-body">
                                        <p class="lead">
                                            <a href="{% if pre_req == 'vca' %}{% url 'vca' %}{% elif pre_req == 'profile' %}{% url 'patient' %}{% else %}{% url 'categories' %}{% endif %}">
                                                <!-- {% if pre_req == 'purchase' %}-->
                                                If you’d another product, please get started by selecting a category.
                                                <!-- {% elif pre_req == 'vca' %}-->
                                                Intake form must be completed prior to filling out your account details.
                                                <!-- {% elif pre_req == 'profile' %}-->
                                                Account details must be completed prior to filling out your intake form.
                                                <!-- {% else %}-->
                                                Please select at least one product to continue.
                                                <!-- {% endif %}-->
                                            </a>
                                        </p>
                                        <p><a href="{% url 'contact_form' %}">Contact Us if you have any questions or if something isn't working as you expected.</a></p>
                                        <div class="row align-items-center justify-content-center">
                                            <div class="col-lg-6">
                                                <div class="d-grid">
                                                    <a class="btn btn-primary btn-lg btn-shadow" type="button" href="{% if pre_req == 'vca' %}{% url 'vca' %}{% elif pre_req == 'profile' %}{% url 'patient' %}{% else %}{% url 'categories' %}{% endif %}">
                                                        <i class="me-2" data-feather="arrow-left"></i>
                                                        <!-- {% if pre_req == 'vca' %}-->
                                                        Complete Intake
                                                        <!-- {% elif pre_req == 'profile' %}-->
                                                        Complete Account Details
                                                        <!-- {% else %}-->
                                                        Categories
                                                        <!-- {% endif %}-->
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- {% elif coupon_text %}-->
                        <div class="modal fade" id="couponModal" tabindex="-1" role="dialog" aria-labelledby="couponModalLabel" aria-hidden="false">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="text-center modal-title" id="couponModalLabel">Coupon</h5>
                                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">{{coupon_text|safe}}</div>
                                    <div class="modal-footer"><button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Close</button></div>
                                </div>
                            </div>
                        </div>
                        <!-- {% endif %}-->
                        <div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="text-center modal-title" id="errorModalLabel">Please click a test to continue</h5>
                                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="text-center"><img class="img-fluid" src="/assets/img/std-test-select.gif" /></div>
                                        <p class="lead mt-3">Or return to see all categories</p>
                                        <div class="row justify-content-center">
                                            <div class="col-sm-6">
                                                <div class="d-grid">
                                                    <a class="btn btn-primary btn-lg" type="button" href="/app/categories/">
                                                        <i class="me-2" data-feather="arrow-left"></i>
                                                        All Categories
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="d-grid"><button class="btn btn-secondary btn-lg" type="button" data-bs-dismiss="modal">Close</button></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- {% if False %}-->
                        <hr class="mt-2 my-4" />
                        <a class="card-link" href="https://teletest.ca">
                            <div class="alert alert-icon shadow alert-orange lift pointer">
                                <div class="alert-icon-aside"><i class="fas fa-triangle-exclamation"></i></div>
                                <div class="alert-icon-content">
                                    <h6 class="alert-heading">THIS IS A TEST SITE</h6>
                                    Ordering is disabled on this site, please see our main site at
                                    <a href="https://teletest.ca">TeleTest.ca</a>
                                </div>
                            </div>
                        </a>
                        <!-- {% endif %}-->
                        <form id="app-form" method="POST">
                            {% csrf_token %}
                            <!-- {% with cart_completed=cart.completed %}-->
                            <hr class="my-4" />
                            <div class="row justify-content-center products">
                                <!-- {% for product in products %}-->
                                <label class="col-lg-6 col-xl-4 mb-4">
                                    <!-- {% if product.pk == cart.product.pk %}-->
                                    <!-- {% if cart_completed %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" checked disabled />
                                    <!-- {% else %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" checked />
                                    <!-- {% endif %}-->
                                    <!-- {% else %}-->
                                    <!-- {% if cart_completed %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" disabled />
                                    <!-- {% else %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" />
                                    <!-- {% endif %}-->
                                    <!-- {% endif %}-->
                                    <div class="card lift h-100 bg-{{product.color}}-soft">
                                        <div class="badge-check btn bg-white btn-icon btn-sm border-md btn-outline-{{product.color}}-soft"><i class="checkmark fas fa-check"></i></div>
                                        <div class="card-header">
                                            <div class="text-{{product.color}}-hard">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="div">
                                                        <div class="pricing-price">{{product.name}}</div>
                                                        <!-- {% if 'adh_' in product.key %}-->
                                                        <div class="badge bg-green-soft rounded-pill badge-marketing badge-sm text-green">Most Popular</div>
                                                        <!-- {% endif %}-->
                                                    </div>
                                                    <div class="div">${{product.price|divide_100|floatformat:"0"}}{% if 'adh_' in product.key %}+{% endif %}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body px-0 pt-2 bg-white">
                                            <div class="px-3 text-gray-700">{{product.info|safe}}</div>
                                            <ul class="ps-2 list-group list-group-flush">
                                                <!-- {% for assay in product.bullets %}{% if assay.product_page != False %}-->
                                                <li class="list-group-item">
                                                    <!-- {%   if assay.test_type == 'coupon' %} -->
                                                    <i class="me-2 fas fa-money-bill-wave text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'blood' %} -->
                                                    <i class="me-2 fas fa-syringe text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'ecg' %} -->
                                                    <i class="me-2 fas fa-heart-pulse text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'imaging' %} -->
                                                    <i class="me-2 fas fa-x-ray text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'urine' %} -->
                                                    <i class="me-2 fas fa-prescription-bottle text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'note' %} -->
                                                    <i class="me-2 fas fa-note-sticky text-{{product.color}}"></i>
                                                    <!-- {% elif 'swab' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-broom text-{{product.color}}"></i>
                                                    <!-- {% elif 'ship' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-shipping-fast text-{{product.color}}"></i>
                                                    <!-- {% elif 'prescription' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-capsules text-{{product.color}}"></i>
                                                    <!-- {% else %} -->
                                                    <i class="me-2 fas fa-vials text-{{product.color}}"></i>
                                                    <!-- {% endif %} -->
                                                    <span class="small">{{assay.name}}</span>
                                                </li>
                                                <!-- {% endif %}{% endfor %}-->
                                            </ul>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <!-- {% if product.more_info_url %}-->
                                            <a class="small" href="{{product.more_info_url}}" target="_blank">
                                                <i class="me-1 vam text-primary" data-feather="info"></i>
                                                <!-- {% if product.more_info_url %}-->
                                                {{product.more_info_txt}}
                                                <!-- {% endif %}-->
                                                <i data-feather="external-link"></i>
                                            </a>
                                            <!-- {% else %}-->
                                            {{product.more_info_txt}}
                                            <!-- {% endif %}-->
                                        </div>
                                    </div>
                                </label>
                                <!-- {% endfor %}-->
                            </div>
                            <!-- {% if add_ons %}-->
                            <h4 class="mt-3">Add-ons</h4>
                            <hr class="my-4" />
                            <div class="row justify-content-center">
                                <!-- {% for product in add_ons %}-->
                                <label class="col-lg-6 col-xl-4 mb-4">
                                    <!-- {% if product.pk == cart.product.pk %}-->
                                    <!-- {% if cart_completed %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" checked disabled />
                                    <!-- {% else %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" checked />
                                    <!-- {% endif %}-->
                                    <!-- {% else %}-->
                                    <!-- {% if cart_completed %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" disabled />
                                    <!-- {% else %}-->
                                    <input class="card-input-element d-none myradio" type="checkbox" name="products" value="{{product.id}}" />
                                    <!-- {% endif %}-->
                                    <!-- {% endif %}-->
                                    <div class="card lift h-100 bg-{{product.color}}-soft">
                                        <div class="badge-check btn bg-white btn-icon btn-sm border-md btn-outline-{{product.color}}-soft"><i class="checkmark fas fa-check"></i></div>
                                        <div class="card-header">
                                            <div class="text-{{product.color}}-hard">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="div">
                                                        <div class="pricing-price">{{product.name}}</div>
                                                        <!-- {% if 'adh_' in product.key %}-->
                                                        <div class="badge bg-green-soft rounded-pill badge-marketing badge-sm text-green">Most Popular</div>
                                                        <!-- {% endif %}-->
                                                    </div>
                                                    <div class="div">${{product.price|divide_100|floatformat:"0"}}{% if 'adh_' in product.key %}+{% endif %}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body px-0 pt-2 bg-white">
                                            <div class="px-3 text-gray-700">{{product.info|safe}}</div>
                                            <ul class="ps-2 list-group list-group-flush">
                                                <!-- {% for assay in product.bullets %}{% if assay.product_page != False %}-->
                                                <li class="list-group-item">
                                                    <!-- {%   if assay.test_type == 'coupon' %} -->
                                                    <i class="me-2 fas fa-money-bill-wave text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'blood' %} -->
                                                    <i class="me-2 fas fa-syringe text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'ecg' %} -->
                                                    <i class="me-2 fas fa-heart-pulse text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'imaging' %} -->
                                                    <i class="me-2 fas fa-x-ray text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'urine' %} -->
                                                    <i class="me-2 fas fa-prescription-bottle text-{{product.color}}"></i>
                                                    <!-- {% elif assay.test_type == 'note' %} -->
                                                    <i class="me-2 fas fa-note-sticky text-{{product.color}}"></i>
                                                    <!-- {% elif 'swab' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-broom text-{{product.color}}"></i>
                                                    <!-- {% elif 'ship' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-shipping-fast text-{{product.color}}"></i>
                                                    <!-- {% elif 'prescription' in assay.test_type %} -->
                                                    <i class="me-2 fas fa-capsules text-{{product.color}}"></i>
                                                    <!-- {% else %} -->
                                                    <i class="me-2 fas fa-vials text-{{product.color}}"></i>
                                                    <!-- {% endif %} -->
                                                    <span class="small">{{assay.name}}</span>
                                                </li>
                                                <!-- {% endif %}{% endfor %}-->
                                            </ul>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <!-- {% if product.more_info_url %}-->
                                            <a class="small" href="{{product.more_info_url}}" target="_blank">
                                                <i class="me-1 vam text-primary" data-feather="info"></i>
                                                <!-- {% if product.more_info_url %}-->
                                                {{product.more_info_txt}}
                                                <!-- {% endif %}-->
                                                <i data-feather="external-link"></i>
                                            </a>
                                            <!-- {% else %}-->
                                            {{product.more_info_txt}}
                                            <!-- {% endif %}-->
                                        </div>
                                    </div>
                                </label>
                                <!-- {% endfor %}-->
                            </div>
                            <!-- {% endif %}{% endwith %}-->
                            <!-- {% if products %}-->
                            <div class="row justify-content-center mb-3">
                                <div class="col-lg-6">
                                    <div class="d-grid">
                                        <button class="err-modal btn btn-primary btn-shadow fw-500 text-lg" id="continue-btn" type="submit" role="button" name="continue-button">
                                            Continue
                                            <i class="feather-lg ms-2" data-feather="arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- {% endif %}-->
                        </form>
                    </div>
                </main>
                <footer class="footer-admin mt-auto footer-light">
                    <div class="container">
                        <div class="row mt-0">
                            <div class="col-md-12">
                                <div class="logo-listing-wrp border-top">
                                    <div class="d-flex flex-wrap align-items-center mt-4 justify-content-center">
                                        <a href="https://www.legitscript.com/websites/?checker_keywords=teletest.ca" target="_blank" title="Verify LegitScript Approval" rel="nofollow"><img src="https://static.legitscript.com/seals/10847275.png" alt="LegitScript approved" width="120" border="0" /></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center text-md-start text-center mt-3 mt-md-4">
                            <div class="col-md-6 small text-dark text-md-end">
                                Copyright &copy; TeleTest
                                <script>
                                    document.write(new Date().getFullYear());
                                </script>
                            </div>
                            <div class="col-md-6 small">
                                <a href="/privacy-policy.html">Privacy Policy</a>
                                &middot;
                                <a href="/terms-of-service.html">Terms of Service</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script src="/js/scripts.js"></script>
        <!-- {% if pre_req %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('requiredModal'), { backdrop: 'static', keyboard: false });
            modal.show();
            if ($('#loading-spinner').length) {
                $('#loading-spinner').hide();
            }
        </script>
        <!-- {% else %}-->
        <!-- {% if coupon_text %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('couponModal'), {});
            modal.show();
            let url = new URL(location.href);
            url.searchParams.delete('coupon');
            window.history.pushState({}, document.title, url);
        </script>
        <!-- {% endif %}-->
        <script>
            //- https://stackoverflow.com/questions/14339805/simulate-radio-buttons-using-checkboxes-and-only-trigger-change-event-once
            $('body').delegate('.products .myradio', 'click', function (e) {
                var $element = $(this)[0];
                $('.myradio').each(function () {
                    if ($(this)[0] !== $element) $(this).prop('checked', false);
                });
            });

            //- continue button with err-modal class will show "how to click test" gif
            $('.err-modal').on('click', function (e) {
                if ($('#app-form input:checkbox:checked').length > 0) {
                    if (e.target.name == 'continue-button') {
                        $('#app-form').submit();
                    }
                } else {
                    e.preventDefault();
                    var modal = new bootstrap.Modal(document.getElementById('errorModal'), {});
                    modal.show();
                }
            });

            let $input = $('#search');
            if ($input.val().length > 0) {
                let length = $input.val().length;
                $input[0].setSelectionRange(length, length); // Move cursor to the end
                $input.focus(); // Ensure the field stays focused
            }
        </script>
        <!-- {% endif %}-->
    </body>
</html>

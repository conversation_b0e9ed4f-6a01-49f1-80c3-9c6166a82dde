<!DOCTYPE html>
<!-- {% load template_extras %}-->
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="description" content="" />
        <meta name="author" content="" />
        <title>Account - Billing - TeleTest.ca</title>
        <link href="/css/styles.min.css?v=2" rel="stylesheet" />
        <link rel="icon" type="image/x-icon" href="/assets/img/favicon.png" />
        <script src="/assets/libs/feather-icons-4.29.0/feather.min.js" crossorigin="anonymous"></script>
        <link href="/assets/libs/font-awesome/css/fontawesome.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/brands.css" rel="stylesheet" />
        <link href="/assets/libs/font-awesome/css/solid.css" rel="stylesheet" />
    </head>
    <body class="nav-fixed">
        <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white" id="sidenavAccordion">
            <a class="navbar-brand logo-text pe-3 ps-1 ps-lg-3" href="/">
                <img class="img-fluid" src="/assets/img/logo/logo.svg" />
                <span class="ms-3">TeleTest.ca</span>
            </a>
            <!-- Sidenav Toggle Button-->
            <button class="btn btn-icon btn-transparent-dark order-1 order-lg-0 me-2 ms-lg-2 me-lg-0" id="sidebarToggle"><i class="feather-lg" data-feather="menu"></i></button>
            <!-- Navbar Items-->
            <ul class="navbar-nav align-items-center ms-auto me-lg-5 me-1">
                <!-- {% if not user.is_authenticated %}-->
                <a class="btn btn-outline-baby-blue rounded-pill px-4 ms-lg-4" id="home-login-button" href="/app/patient-portal/">Login</a>
                <!-- {% else %}-->
                <!-- User Dropdown-->
                <li class="nav-item dropdown no-caret dropdown-user me-3 me-lg-4">
                    <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage" href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><img class="img-fluid" src="/assets/img/icons/users/user-0c.svg" /></a>
                    <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up" aria-labelledby="navbarDropdownUserImage">
                        <h6 class="dropdown-header d-flex align-items-center">
                            <img class="dropdown-user-img" src="/assets/img/icons/users/user-0c.svg" />
                            <div class="dropdown-user-details">
                                <div class="dropdown-user-details-name">{{user.profile.name}}</div>
                                <div class="dropdown-user-details-email">{{user.email}}</div>
                            </div>
                        </h6>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="/app/account-details/">
                            <div class="dropdown-item-icon"><i data-feather="settings"></i></div>
                            Account
                        </a>
                        <a class="dropdown-item" href="/app/accounts/logout/">
                            <div class="dropdown-item-icon"><i data-feather="log-out"></i></div>
                            Logout
                        </a>
                    </div>
                </li>
                <!-- {% endif %}-->
            </ul>
        </nav>
        <div id="layoutSidenav">
            <div id="layoutSidenav_nav">
                <!-- if categories-->
                <!-- {% if logout_redirect and logout_redirect == False %}-->
                <!-- false-->
                <!-- {% else %}-->
                <!-- true-->
                {{ redirect_to_login_immediately }}
                <!-- {% endif %}-->
                <nav class="sidenav shadow-right sidenav-light">
                    <div class="sidenav-menu">
                        <div class="nav accordion" id="accordionSidenav">
                            <a class="nav-link" href="/app/patient-portal/">
                                <div class="nav-link-icon"><i data-feather="home"></i></div>
                                Patient Portal
                            </a>
                            <a class="nav-link" href="/app/contact/">
                                <div class="nav-link-icon"><i data-feather="phone"></i></div>
                                Contact Us
                            </a>
                            <a class="nav-link" href="/app/about/">
                                <div class="nav-link-icon"><i data-feather="smile"></i></div>
                                About Us
                            </a>
                            <a class="nav-link" href="https://docs.teletest.ca/" target="_blank">
                                <div class="nav-link-icon"><i data-feather="help-circle"></i></div>
                                FAQ
                            </a>
                            <a class="nav-link" href="/blog/">
                                <div class="nav-link-icon"><i data-feather="bold"></i></div>
                                Blog
                            </a>
                            <a class="nav-link" href="/app/lab-testing/ON/cities/">
                                <div class="nav-link-icon"><i data-feather="clock"></i></div>
                                Lab Times
                            </a>
                            <!-- {% if user.is_authenticated %}-->
                            <a class="nav-link" href="/app/provider-info/">
                                <div class="nav-link-icon"><i class="fas fa-notes-medical"></i></div>
                                Provider Info
                            </a>
                            <!-- {% endif %}-->
                            <div class="sidenav-menu-heading">Healthcare</div>
                            <a class="nav-link" href="/app/categories/">
                                <div class="nav-link-icon"><i data-feather="layers"></i></div>
                                Categories
                            </a>
                            <!-- {% if not categories %}-->
                            <a class="nav-link" href="/app/care/std/">
                                <div class="nav-link-icon"><i class="fas fa-venus-mars"></i></div>
                                STD
                            </a>
                            <!-- {% else %} {% for category in categories %}-->
                            <a class="nav-link" href="{{category.url}}">
                                <div class="nav-link-icon"><i class="fas fa-{{category.icon}}"></i></div>
                                {{category.name}}
                            </a>
                            <!-- {% endfor %} {% endif %}-->
                            <div class="sidenav-menu-heading">Intake</div>
                            <a class="nav-link" href="/app/intake/">
                                <div class="nav-link-icon"><i class="fas fa-stethoscope"></i></div>
                                Virtual Care Assessment
                            </a>
                            <a class="nav-link" href="/app/account-details/">
                                <div class="nav-link-icon"><i data-feather="user"></i></div>
                                Account Details
                            </a>
                            <a class="nav-link" href="/app/find-location/lab/">
                                <div class="nav-link-icon"><i class="fas fa-search-location"></i></div>
                                Find Lab
                            </a>
                            <a class="nav-link" href="/app/payment/">
                                <div class="nav-link-icon"><i class="fas fa-credit-card"></i></div>
                                Checkout
                            </a>
                        </div>
                    </div>
                    <div class="sidenav-footer">
                        <div class="sidenav-footer-content">
                            <!-- {% if user.is_authenticated %}-->
                            <div class="sidenav-footer-subtitle">Logged in as:</div>
                            <div class="sidenav-footer-title profileEmail"></div>
                            {{user.email}}
                            <!-- {% endif %}-->
                        </div>
                    </div>
                </nav>
            </div>
            <div id="layoutSidenav_content">
                <main>
                    <header class="page-header page-header-compact page-header-light border-bottom bg-white mb-2">
                        <div class="container-xl">
                            <div class="page-header-content">
                                <div class="row align-items-center justify-content-between pt-3">
                                    <div class="col-auto mb-3">
                                        <h1 class="page-header-title">
                                            <div class="page-header-icon"><i data-feather="user"></i></div>
                                            Account - Billing
                                        </h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>
                    <div class="container">
                        <nav class="nav nav-borders">
                            <a class="nav-link ms-0" href='{% url "patient" %}'>Details</a>
                            <a class="nav-link" href='{% url "pmh" %}'>History</a>
                            <a class="nav-link" href='{% url "patient-portal" %}'>Portal</a>
                            <a class="nav-link" href='{% url "inbox" %}'>
                                Inbox
                                <!-- {% if unread_count %}-->
                                <div class="btn btn-icon btn-xs btn-blue">{{unread_count}}</div>
                                <!-- {% endif %}-->
                            </a>
                            <a class="nav-link ms-0 active" href='{% url "account-billing" %}'>Billing</a>
                        </nav>
                        <hr class="mt-0 mb-3" />
                        <!-- {% if messages %} {% for message in messages %}-->
                        <div class="alert alert-dismissible fade show alert-{{ message.tags|default:'info' }}" role="alert">
                            {{ message }}
                            <button class="btn-close" type="button" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <!-- {% endfor %} {% endif %}-->
                        <div class="modal fade" id="addPaymentModal" tabindex="-1" role="dialog">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Add Payment Method</h5>
                                        <button class="btn-close" type="button" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="add-pm-form">
                                            <div id="card-element"></div>
                                            <div class="d-grid mt-3"><button class="btn btn-primary" id="add-pm-btn" type="button">Save Card</button></div>
                                        </form>
                                    </div>
                                    <div class="modal-footer"><small class="text-muted">Your card details are stored by Stripe only</small></div>
                                </div>
                            </div>
                        </div>
                        <!-- {% if False %}-->
                        <hr class="mt-2 my-4" />
                        <a class="card-link" href="https://teletest.ca">
                            <div class="alert alert-icon shadow alert-orange lift pointer">
                                <div class="alert-icon-aside"><i class="fas fa-triangle-exclamation"></i></div>
                                <div class="alert-icon-content">
                                    <h6 class="alert-heading">THIS IS A TEST SITE</h6>
                                    Ordering is disabled on this site, please see our main site at
                                    <a href="https://teletest.ca">TeleTest.ca</a>
                                </div>
                            </div>
                        </a>
                        <!-- {% endif %}-->
                        <form id="app-form" method="POST">
                            {% csrf_token %}
                            <!-- Payment methods card-->
                            <div class="card card-header-actions mb-4">
                                <div class="card-header">
                                    Payment Methods
                                    <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#addPaymentModal">Add Payment Method</button>
                                </div>
                                <!-- {% if payment_methods %}-->
                                <div class="card-body px-0">
                                    <!-- {% for pm in payment_methods %}-->
                                    <div class="d-flex align-items-center justify-content-between px-4">
                                        <div class="d-flex align-items-center">
                                            <i class="fab fa-2x fa-cc-{{ pm.card.brand }}"></i>
                                            <div class="ms-4">
                                                <div class="small">{{ pm.card.brand|title }} ending in {{ pm.card.last4 }}</div>
                                                <div class="text-xs text-muted">Expires {{ pm.card.exp_month }}/{{ pm.card.exp_year }}</div>
                                            </div>
                                        </div>
                                        <div class="ms-4 small">
                                            <!-- {% if pm.id == default_pm %}-->
                                            <div class="badge bg-light text-dark me-3">Default</div>
                                            <!-- {% else %}-->
                                            <button class="btn btn-link p-0 text-muted me-3" type="submit" name="make-default" value="{{pm.id}}">Make Default</button>
                                            <!-- {% endif %}-->
                                            <button class="btn btn-link p-0 text-muted text-danger me-3" type="submit" name="delete" value="{{pm.id}}">Delete</button>
                                        </div>
                                    </div>
                                    <!-- {% endfor %}-->
                                </div>
                                <!-- {% else %}-->
                                <div class="card-body">
                                    <p>
                                        Click
                                        <a data-bs-toggle="modal" data-bs-target="#addPaymentModal" href="#">Add Payment Method</a>
                                        to get started.
                                    </p>
                                    <p>Your payment methods will be shown here.</p>
                                </div>
                                <!-- {% endif %}-->
                            </div>
                            <!-- Billing history card-->
                            <!-- {% if payment_methods %}-->
                            <div class="row justify-content-center">
                                <div class="col-lg-6">
                                    <div class="d-grid">
                                        <button class="err-modal btn btn-primary btn-shadow fw-500 text-lg" id="continue-btn" type="submit" role="button" name="continue-button">
                                            Continue
                                            <i class="feather-lg ms-2" data-feather="arrow-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- {% endif %}-->
                        </form>
                    </div>
                </main>
            </div>
        </div>
        <script src="/assets/libs/jquery/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
        <script src="/assets/libs/bootstrap/bootstrap-5.2.3.bundle.min.js" crossorigin="anonymous"></script>
        <script>
            var readFromStorage = function (key) {
                if (!window.Storage) {
                    // From: https://stackoverflow.com/a/15724300/2367037
                    var value = '; ' + document.cookie;
                    var parts = value.split('; ' + key + '=');
                    if (parts.length === 2) {
                        return parts.pop().split(';').shift();
                    }
                } else {
                    return window.localStorage.getItem(key);
                }
            };
            var writeToStorage = function (key, value) {
                if (window.Storage) {
                    window.localStorage.setItem(key, value);
                }

                var expireDays = 365;
                var expiresDate = new Date();
                expiresDate.setDate(expiresDate.getDate() + expireDays);
                document.cookie = key + '=' + value + ';expires=' + expiresDate.toUTCString();
            };
            var deleteFromStorage = function (key) {
                if (window.Storage) {
                    window.localStorage.removeItem(key);
                }

                // Delete cookie by setting it to expire in the past
                document.cookie = key + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;';
            };
        </script>
        <!-- {% with url_name=request.resolver_match.url_name %}{% if not user.is_authenticated and url_name != 'auth_login' and url_name != 'password_reset' %}-->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-WT0R73QKDH"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', 'G-WT0R73QKDH');
        </script>
        <!-- {% endif %}{% endwith %}-->
        <script src="/js/scripts.js"></script>
        <!-- {% if pre_req %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('requiredModal'), { backdrop: 'static', keyboard: false });
            modal.show();
            if ($('#loading-spinner').length) {
                $('#loading-spinner').hide();
            }
        </script>
        <!-- {% else %}-->
        <!-- {% if coupon_text %}-->
        <script>
            var modal = new bootstrap.Modal(document.getElementById('couponModal'), {});
            modal.show();
            let url = new URL(location.href);
            url.searchParams.delete('coupon');
            window.history.pushState({}, document.title, url);
        </script>
        <!-- {% endif %}-->
        <script src="https://js.stripe.com/v3/"></script>
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const stripe = Stripe('{{ stripe_public_key }}');
                const elements = stripe.elements();
                const card = elements.create('card');
                card.mount('#card-element');
                // start Stripe SetupIntent
                async function getClientSecret() {
                    const rsp = await fetch('{% url "account-billing" %}', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': '{{ csrf_token }}',
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=create_setup_intent',
                    });
                    return (await rsp.json()).client_secret;
                }
                // save new card
                document.getElementById('add-pm-btn').addEventListener('click', async () => {
                    try {
                        const clientSecret = await getClientSecret();
                        const { setupIntent, error } = await stripe.confirmCardSetup(clientSecret, {
                            payment_method: { card },
                        });
                        if (error) return alert(error.message);
                        const form = Object.assign(document.createElement('form'), { method: 'POST', action: '{% url "account-billing" %}' });
                        form.innerHTML = `<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                 <input type="hidden" name="action" value="attach_payment_method">
                 <input type="hidden" name="pm_id" value="${setupIntent.payment_method}">
                 <input type="hidden" name="make_default" value="true">`;
                        document.body.appendChild(form);
                        form.submit();
                    } catch (err) {
                        console.error(err);
                        alert('Something went wrong while saving your card.');
                    }
                });
            });
        </script>
        <!-- {% endif %}-->
    </body>
</html>
